from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ger,
    Foreign<PERSON>ey,
)
from sqlalchemy.orm import relationship
from app.models.base import Base


class OrganizationSubscription(Base):
    __tablename__ = "organization_subscriptions"

    id = Column(String, primary_key=True)
    organization_id = Column(String, ForeignKey("organizations.id"), nullable=False, unique=True)
    subscription_plan_id = Column(String, ForeignKey("subscription_plans.id"), nullable=False)
    interviews_used = Column(Integer, default=0)
    activated_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)  # For future time-based subscriptions
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    organization = relationship("Organization", back_populates="subscription")
    subscription_plan = relationship("SubscriptionPlan", back_populates="organization_subscriptions")

    def __repr__(self):
        return f"<OrganizationSubscription {self.organization_id} - {self.interviews_used} used>"

    @property
    def interviews_remaining(self):
        """Calculate remaining interviews based on plan limit and usage."""
        if self.subscription_plan:
            return max(0, self.subscription_plan.interview_limit - self.interviews_used)
        return 0

    @property
    def is_limit_reached(self):
        """Check if organization has reached their interview limit."""
        return self.interviews_remaining <= 0

    def can_schedule_interview(self):
        """Check if organization can schedule another interview."""
        return self.is_active and not self.is_limit_reached