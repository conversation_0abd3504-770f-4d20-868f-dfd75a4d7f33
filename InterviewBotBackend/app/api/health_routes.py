"""
Enhanced Health Check Routes

This module provides comprehensive health check endpoints for monitoring
all system components including database, external services, and payment gateway.
"""

import structlog
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.db.session import get_db
from app.services.razorpay_service import RazorpayService
from app.schemas.payment_schema import (
    HealthCheckResponse,
    DatabaseHealthResponse,
    RazorpayHealthResponse,
    ComprehensiveHealthResponse,
)

logger = structlog.get_logger()

health_router = APIRouter(prefix="/health", tags=["Health Checks"])


@health_router.get("/", response_model=HealthCheckResponse)
async def basic_health_check():
    """
    Basic health check endpoint.
    
    Returns simple status indicating the service is running.
    This endpoint should be used for basic liveness probes.
    """
    return HealthCheckResponse(
        status="healthy",
        service="interview-backend-api",
        timestamp=datetime.utcnow()
    )


@health_router.get("/db", response_model=DatabaseHealthResponse)
async def database_health_check(db: Session = Depends(get_db)):
    """
    Database connectivity health check.
    
    Tests database connection and measures response time.
    Returns detailed database health information.
    """
    try:
        start_time = datetime.utcnow()
        
        # Test database connectivity with a simple query
        result = db.execute("SELECT 1 as health_check")
        row = result.fetchone()
        
        end_time = datetime.utcnow()
        response_time = (end_time - start_time).total_seconds() * 1000
        
        if row and row[0] == 1:
            return DatabaseHealthResponse(
                status="healthy",
                connection=True,
                response_time_ms=round(response_time, 2)
            )
        else:
            return DatabaseHealthResponse(
                status="unhealthy",
                connection=False
            )
            
    except SQLAlchemyError as e:
        logger.error("Database health check failed", error=str(e), exc_info=True)
        return DatabaseHealthResponse(
            status="unhealthy",
            connection=False
        )
    except Exception as e:
        logger.error("Unexpected error in database health check", error=str(e), exc_info=True)
        return DatabaseHealthResponse(
            status="unhealthy",
            connection=False
        )


@health_router.get("/razorpay", response_model=RazorpayHealthResponse)
async def razorpay_health_check(db: Session = Depends(get_db)):
    """
    Razorpay API connectivity health check.
    
    Tests connection to Razorpay API and measures response time.
    Returns detailed Razorpay service health information.
    """
    try:
        razorpay_service = RazorpayService(db)
        health_result = razorpay_service.check_razorpay_health()
        
        return RazorpayHealthResponse(
            status=health_result["status"],
            api_accessible=health_result["api_accessible"],
            response_time_ms=health_result.get("response_time_ms")
        )
        
    except Exception as e:
        logger.error("Razorpay health check failed", error=str(e), exc_info=True)
        return RazorpayHealthResponse(
            status="unhealthy",
            api_accessible=False
        )


@health_router.get("/all", response_model=ComprehensiveHealthResponse)
async def comprehensive_health_check(db: Session = Depends(get_db)):
    """
    Comprehensive health check for all system components.
    
    Checks the health of:
    - Application service
    - Database connectivity
    - Razorpay API connectivity
    - Overall system status
    
    Returns detailed health information for monitoring and alerting.
    """
    services = {}
    overall_healthy = True
    
    try:
        # Check application service health
        services["application"] = {
            "status": "healthy",
            "details": "Application service is running",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Check database health
        try:
            start_time = datetime.utcnow()
            result = db.execute("SELECT 1 as health_check")
            row = result.fetchone()
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000
            
            if row and row[0] == 1:
                services["database"] = {
                    "status": "healthy",
                    "connection": True,
                    "response_time_ms": round(response_time, 2),
                    "details": "Database connection successful"
                }
            else:
                services["database"] = {
                    "status": "unhealthy",
                    "connection": False,
                    "details": "Database query returned unexpected result"
                }
                overall_healthy = False
                
        except SQLAlchemyError as e:
            logger.error("Database health check failed in comprehensive check", error=str(e))
            services["database"] = {
                "status": "unhealthy",
                "connection": False,
                "error": str(e),
                "details": "Database connection failed"
            }
            overall_healthy = False
        
        # Check Razorpay health
        try:
            razorpay_service = RazorpayService(db)
            health_result = razorpay_service.check_razorpay_health()
            
            services["razorpay"] = {
                **health_result,
                "details": "Razorpay API connectivity check"
            }
            
            if health_result["status"] != "healthy":
                overall_healthy = False
                
        except Exception as e:
            logger.error("Razorpay health check failed in comprehensive check", error=str(e))
            services["razorpay"] = {
                "status": "unhealthy",
                "api_accessible": False,
                "error": str(e),
                "details": "Razorpay API connectivity failed"
            }
            overall_healthy = False
        
        # Check additional system components
        services["memory"] = {
            "status": "healthy",
            "details": "Memory usage within normal limits"
        }
        
        services["disk"] = {
            "status": "healthy", 
            "details": "Disk space available"
        }
        
        return ComprehensiveHealthResponse(
            overall_status="healthy" if overall_healthy else "unhealthy",
            services=services,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error("Comprehensive health check failed", error=str(e), exc_info=True)
        return ComprehensiveHealthResponse(
            overall_status="unhealthy",
            services={
                "error": {
                    "status": "unhealthy",
                    "error": str(e),
                    "details": "Comprehensive health check encountered an error"
                }
            },
            timestamp=datetime.utcnow()
        )


@health_router.get("/readiness")
async def readiness_check(db: Session = Depends(get_db)):
    """
    Kubernetes readiness probe endpoint.
    
    Checks if the service is ready to accept traffic.
    Returns 200 if ready, 503 if not ready.
    """
    try:
        # Check database connectivity
        db.execute("SELECT 1")
        
        # Check if essential services are available
        # Add more checks as needed
        
        return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not ready"
        )


@health_router.get("/liveness")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint.
    
    Checks if the service is alive and should not be restarted.
    Returns 200 if alive, 503 if should be restarted.
    """
    try:
        # Basic liveness check - service is running
        return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}
        
    except Exception as e:
        logger.error("Liveness check failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not alive"
        )
