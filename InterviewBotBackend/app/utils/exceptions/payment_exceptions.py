"""
Payment Exception Classes

This module defines custom exception classes for payment-related errors
in the Razorpay integration system.
"""


class PaymentException(Exception):
    """Base exception class for payment-related errors."""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class OrderCreationException(PaymentException):
    """Exception raised when order creation fails."""
    pass


class PaymentVerificationException(PaymentException):
    """Exception raised when payment verification fails."""
    pass


class WebhookProcessingException(PaymentException):
    """Exception raised when webhook processing fails."""
    pass


class InvalidSignatureException(PaymentException):
    """Exception raised when signature verification fails."""
    pass


class SubscriptionPlanNotFoundException(PaymentException):
    """Exception raised when subscription plan is not found."""
    pass


class OrderNotFoundException(PaymentException):
    """Exception raised when order is not found."""
    pass


class TransactionNotFoundException(PaymentException):
    """Exception raised when transaction is not found."""
    pass


class RazorpayAPIException(PaymentException):
    """Exception raised when Razorpay API calls fail."""
    pass


class PaymentGatewayException(PaymentException):
    """Exception raised for general payment gateway errors."""
    pass


class DuplicateWebhookException(PaymentException):
    """Exception raised when duplicate webhook events are received."""
    pass


class InsufficientPermissionException(PaymentException):
    """Exception raised when user lacks permission for payment operations."""
    pass
