from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Request, Query, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.services.user_service import UserService
from app.schemas.user_schema import (
    OrganizationUpdateSkillsJobsSchema,
    RegistrationCreate,
    RegisterResponse,
    RegistrationType,
    TokenResponse,
    UserProfileUpdate,
    UserUpdateJobDescriptionsSchema,
)
from app.schemas.email_verification_schema import (
    VerifyOTPRequest,
    ResendOTPRequest,
    EmailVerificationResponse,
    OTPVerificationResponse
)
from fastapi.responses import RedirectResponse, JSONResponse
from app.db.session import SessionLocal, get_db
from app.core.config import settings
from app.core.auth_guard import BaseAuthGuard, roles_required
from pydantic import ValidationError
import json
from urllib.parse import unquote, urlencode, quote
from app.models.organization import Organization

# Routers
auth_router = APIRouter(prefix="/auth", tags=["auth"])
user_router = APIRouter(prefix="/users", tags=["users"])
organization_router = APIRouter(prefix="/organization", tags=["organizations"])
user_service = UserService(SessionLocal())

# --- Authentication Routes ---


@auth_router.post(
    "/register",
    summary="Register a New User or Organization",
    description="""
    This endpoint allows a new user or organization to register by providing their email, password, full name (for individuals) or organization name (for organizations), and registration type.

    - The email must be unique and not already registered in the system.
    - The registration type must be either 'individual' or 'organization'.
    - On success, returns authentication details along with user or organization info.
    - On failure, raises an appropriate error with details (e.g., duplicate email, invalid registration type).
    """,
    response_model=RegisterResponse,
    responses={
        200: {
            "description": "User or Organization successfully registered",
            "content": {
                "application/json": {
                    "examples": {
                        "Individual": {
                            "summary": "Successful Individual Registration",
                            "value": {
                                "success": True,
                                "message": "User registered successfully",
                                "access_token": "eyJhbGciOiJIUzI1...",
                                "token_type": "bearer",
                                "registration_type": "individual",
                                "email": "<EMAIL>",
                                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                                "full_name": "John Doe",
                                "created_at": "2024-04-04T12:34:56Z",
                            },
                        },
                        "Organization": {
                            "summary": "Successful Organization Registration",
                            "value": {
                                "success": True,
                                "message": "Organization registered successfully",
                                "access_token": "eyJhbGciOiJIUzI1...",
                                "token_type": "bearer",
                                "registration_type": "organization",
                                "email": "<EMAIL>",
                                "organization_id": "789e4567-e89b-12d3-a456-426614174111",
                                "organization_name": "Acme Corp",
                                "created_at": "2024-04-04T12:34:56Z",
                                "skill_sets": ["Blockchain", "AI"],
                                "job_roles": ["Developer", "Researcher"],
                                "is_setup_complete": True,
                            },
                        },
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid input data or registration type",
            "content": {"application/json": {"example": {"detail": "Invalid registration type"}}},
        },
        409: {
            "description": "Conflict - Email already registered",
            "content": {
                "application/json": {
                    "example": {"detail": "Email <EMAIL> is already registered"}
                }
            },
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {
                "application/json": {"example": {"detail": "Error during user registration"}}
            },
        },
    },
)
async def register(user_data: RegistrationCreate):
    try:

        response = await user_service.register_individual_or_organization(
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name,
            registration_type=user_data.registration_type,
        )

        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@auth_router.post(
    "/login",
    summary="User or Organization Login",
    description="""
    This endpoint authenticates a registered user or organization and returns an access token.

    - Accepts email (as username) and password using OAuth2 password flow.
    - Email is case-insensitive (converted to lowercase).
    - On success, returns an access token, token type, and user details.
    - Supports both individual users and organizations.
    - On failure, raises appropriate error messages.

    **Request Format (Form Data):**
    - `username`: User or organization's email address (required)
    - `password`: User's password (required)

    Example Request (x-www-form-urlencoded):
    ```
    username=<EMAIL>
    password=yourpassword
    ```
    """,
    response_model=TokenResponse,
    responses={
        200: {
            "description": "User successfully authenticated",
            "content": {
                "application/json": {
                    "examples": {
                        "Individual": {
                            "summary": "Successful Individual Login",
                            "value": {
                                "success": True,
                                "message": "Login successful",
                                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                                "token_type": "bearer",
                                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                                "email": "<EMAIL>",
                                "full_name": "John Doe",
                            },
                        },
                        "Organization": {
                            "summary": "Successful Organization Login",
                            "value": {
                                "success": True,
                                "message": "Login successful",
                                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                                "token_type": "bearer",
                                "organization_id": "789e4567-e89b-12d3-a456-426614174111",
                                "email": "<EMAIL>",
                                "organization_name": "Acme Corp",
                            },
                        },
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid input data",
            "content": {"application/json": {"example": {"detail": "Invalid email or password"}}},
        },
        401: {
            "description": "Unauthorized - Invalid credentials",
            "content": {"application/json": {"example": {"detail": "Incorrect email or password"}}},
        },
        500: {
            "description": "Internal Server Error - Authentication service issue",
            "content": {"application/json": {"example": {"detail": "Error during login"}}},
        },
    },
)
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    try:
        response = await user_service.login(
            email=form_data.username.lower(), password=form_data.password
        )
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@auth_router.get(
    "/google-login",
    summary="Initiate Google OAuth Login",
    description="""
    Initiates the Google OAuth flow by redirecting to Google's authorization page.

    - Users can log in via Google and grant permissions.
    - The `registration_type` parameter (optional) specifies whether the login is for an individual or an organization.
    """,
    responses={
        302: {
            "description": "Redirects to Google OAuth authorization page",
            "headers": {
                "Location": {
                    "description": "URL to Google's OAuth authorization page",
                    "schema": {"type": "string"},
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid registration type",
            "content": {"application/json": {"example": {"detail": "Invalid registration type"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def google_auth(reg_type: Optional[RegistrationType] = None):
    """
    Initiates the Google OAuth flow by redirecting to Google's authorization page
    """
    try:
        # Google OAuth authorization URL
        auth_url = "https://accounts.google.com/o/oauth2/auth"

        state_payload = {"registration_type": reg_type} if reg_type else {}
        state = json.dumps(state_payload)

        # Parameters for the authorization request
        params = {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "response_type": "code",
            "scope": "email profile",
            "redirect_uri": settings.GOOGLE_REDIRECT_URI,
            "access_type": "offline",
            "prompt": "consent",
            "state": state,  # Pass extra info through 'state'
        }

        # Construct the URL with parameters
        url = auth_url + "?" + "&".join([f"{key}={params[key]}" for key in params])

        # Redirect to Google's authorization page
        return RedirectResponse(url)

    except Exception:
        # Generic error message for server errors
        raise HTTPException(status_code=500, detail="Internal server error")


@auth_router.get(
    "/google-callback",
    summary="Handle Google OAuth Callback",
    description="""
    This endpoint handles the callback from Google OAuth after the user authorizes the application.

    - It exchanges the authorization code for access and refresh tokens.
    - It retrieves user information from Google and logs the user in or registers them.
    - On success, it redirects to the frontend with authentication token and user details as URL parameters.
    - On error, it redirects to the frontend error page with error information.
    """,
    responses={
        302: {
            "description": "Redirects to frontend with authentication details",
            "headers": {
                "Location": {
                    "description": "Frontend URL with token and user details as query parameters",
                    "schema": {
                        "type": "string",
                        "example": "http://localhost:3000/dashboard?token=eyJhbGciOiJIUzI1...&user_id=123e4567-e89b-12d3-a456-426614174000&type=individual&success=true"
                    },
                }
            },
        },
        302: {
            "description": "Redirects to frontend error page on failure",
            "headers": {
                "Location": {
                    "description": "Frontend error URL with error details",
                    "schema": {
                        "type": "string",
                        "example": "http://localhost:3000/login?error=oauth_failed&message=Error+message"
                    },
                }
            },
        },
    },
)
async def google_callback(code: str, state: Optional[str] = None):
    """
    Handles the callback from Google OAuth and redirects to frontend with token
    """
    try:
        # Decode and parse state
        reg_type = None
        if state:
            decoded = json.loads(unquote(state))
            reg_type = decoded.get("registration_type")

        # Call the existing service to process the auth code
        response = await user_service.google_oauth_login(code, reg_type=reg_type)

        # Extract token and user info from response
        access_token = response.get("access_token")
        user_id = response.get("user_id") or response.get("organization_id")
        registration_type = response.get("registration_type")

        # Construct redirect URL with token and user info using environment variable
        redirect_url = f"{settings.FRONTEND_SUCCESS_REDIRECT_URL}?token={access_token}&user_id={user_id}&type={registration_type}&success=true"

        return RedirectResponse(url=redirect_url)

    except Exception as e:
        # Redirect to frontend with error
        error_redirect_url = (
            f"{settings.FRONTEND_ERROR_REDIRECT_URL}?error=oauth_failed&message={str(e)}"
        )
        return RedirectResponse(url=error_redirect_url)


@user_router.get(
    "/me",
    summary="Get Current User or Organization Information",
    description="""
    This endpoint retrieves information about the currently authenticated user or organization.

    - Requires a valid JWT Bearer token in the `Authorization` header (e.g., `Bearer <token>`).
    - The token must be decoded using the JWT secret and validated against an active session in Redis.
    - Users with `individual` registration type will receive personal details including all profile information.
    - For individual users, the response includes all profile fields: position, location, bio, website, social media links, custom links, and profile picture URL.
    - Users with `organization` registration type will receive organization details, including skill sets and job roles.
    - Errors are returned if the token is invalid, session is missing, permissions are insufficient, or the user is not found.
    """,
    responses={
        200: {
            "description": "User or Organization information retrieved successfully",
            "content": {
                "application/json": {
                    "examples": {
                        "individual": {
                            "summary": "Individual User Response",
                            "value": {
                                "success": True,
                                "message": "User retrieved successfully",
                                "registration_type": "individual",
                                "user_id": "12345",
                                "email": "<EMAIL>",
                                "full_name": "John Doe",
                                "position": "Product Design Lead",
                                "location": "London, UK",
                                "phone_number": "+97-6985472310",
                                "bio": "Experienced product designer with 5+ years in the industry.",
                                "website": "https://myportfolio.com",
                                "instagram": "@designerhandle",
                                "tiktok": "@designertiktok",
                                "youtube": "@designerchannel",
                                "twitter": "@designertwitter",
                                "linkedin": "https://linkedin.com/in/designer",
                                "custom_links": [
                                    {"title": "Portfolio", "url": "https://myportfolio.com/work"},
                                    {"title": "Blog", "url": "https://myblog.com"}
                                ],
                                "profile_picture": "uploads/12345/abc123_profile.jpg",
                                "created_at": "2024-03-31T12:00:00Z",
                                "updated_at": "2024-04-01T10:00:00Z",
                            },
                        },
                        "organization": {
                            "summary": "Organization Response",
                            "value": {
                                "success": True,
                                "message": "Organization retrieved successfully",
                                "registration_type": "organization",
                                "organization_id": "67890",
                                "email": "<EMAIL>",
                                "organization_name": "Acme Corp",
                                "skill_sets": ["Python", "Project Management"],
                                "job_roles": ["Software Engineer", "Manager"],
                                "is_setup_complete": True,
                                "created_at": "2024-03-31T12:00:00Z",
                            },
                        },
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Invalid, expired, or missing JWT token, or no active session in Redis",
            "content": {"application/json": {"example": {"detail": "Unauthorized"}}},
        },
        403: {
            "description": "Forbidden - Missing JWT Bearer token or insufficient role permissions (requires 'user' role)",
            "content": {"application/json": {"example": {"detail": "JWT Bearer is missing"}}},
            "headers": {
                "WWW-Authenticate": {
                    "description": "Bearer token required",
                    "schema": {"type": "string", "example": "Bearer"},
                }
            },
        },
        404: {
            "description": "User or Organization not found",
            "content": {"application/json": {"example": {"detail": "User not found"}}},
        },
        422: {
            "description": "Validation Error",
            "content": {"application/json": {"example": {"detail": "Invalid input data"}}},
        },
        500: {
            "description": "Internal Server Error",
            "content": {"application/json": {"example": {"detail": "Internal server error"}}},
        },
    },
)
async def get_current_user_info(
    current_user: Dict[str, Any] = Depends(BaseAuthGuard()),
):
    try:
        response=None
        if(current_user["registration_type"]==RegistrationType.individual):
            response = user_service.get_user_by_id(current_user["user_id"])
        else:
            response = user_service.get_org_by_id(current_user["user_id"])

        return JSONResponse(content=response)

    except ValidationError as ve:
        # Catch Pydantic validation errors
        raise HTTPException(status_code=422, detail=ve.errors())
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@user_router.put(
    "/update-profile",
    summary="Update user or organization profile information",
    description="""
    This endpoint allows users or organizations to update their profile information.

    - Authenticated users (individual or organization) can access this endpoint.
    - Requires a valid JWT Bearer token.
    - All fields are optional - only the provided fields will be updated.
    - Profile fields include position, location, bio, website, social media links, etc.
    - For individual users: Can also update employment history and skills
    - Employment history: LinkedIn-style work experience with company, position, dates, description, and location
    - Skills: List of user's technical and professional skills
    - The endpoint automatically determines whether to update a user or organization profile based on the authentication token.
    """,
    responses={
        200: {
            "description": "Profile updated successfully",
            "content": {
                "application/json": {
                    "examples": {
                        "individual": {
                            "summary": "Individual User Profile Update",
                            "value": {
                                "success": True,
                                "message": "User profile updated successfully",
                                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                                "email": "<EMAIL>",
                                "full_name": "John Doe",
                                "position": "Product Design Lead",
                                "location": "London, UK",
                                "phone_number": "+97-6985472310",
                                "bio": "Experienced product designer with 5+ years in the industry.",
                                "website": "https://myportfolio.com",
                                "instagram": "@designerhandle",
                                "tiktok": "@designertiktok",
                                "youtube": "@designerchannel",
                                "twitter": "@designertwitter",
                                "linkedin": "https://linkedin.com/in/designer",
                                "profile_picture": "uploads/123e4567-e89b-12d3-a456-426614174000/abc123_profile.jpg",
                                "employment_history": [
                                    {
                                        "id": "emp_20240101_12345678",
                                        "company": "Google",
                                        "position": "Senior Software Engineer",
                                        "start_date": "2022-03",
                                        "end_date": "2024-01",
                                        "current": False,
                                        "description": "Led development of scalable web applications using React and Node.js.",
                                        "location": "Mountain View, CA"
                                    },
                                    {
                                        "id": "emp_20240102_87654321",
                                        "company": "Microsoft",
                                        "position": "Software Engineer",
                                        "start_date": "2020-06",
                                        "end_date": "2022-02",
                                        "current": False,
                                        "description": "Developed cloud-based solutions using Azure services.",
                                        "location": "Seattle, WA"
                                    }
                                ],
                                "skills": [
                                    "Python",
                                    "JavaScript",
                                    "React",
                                    "Node.js",
                                    "AWS",
                                    "Docker",
                                    "Project Management",
                                    "UI/UX Design"
                                ],
                                "created_at": "2024-04-04T12:34:56Z",
                                "updated_at": "2024-04-05T10:11:12Z"
                            }
                        },
                        "organization": {
                            "summary": "Organization Profile Update",
                            "value": {
                                "success": True,
                                "message": "Organization profile updated successfully",
                                "organization_id": "123e4567-e89b-12d3-a456-426614174000",
                                "email": "<EMAIL>",
                                "organization_name": "Acme Corp",
                                "position": "Tech Company",
                                "location": "San Francisco, CA",
                                "phone_number": "******-123-4567",
                                "bio": "Leading technology company specializing in AI solutions.",
                                "website": "https://company.com",
                                "instagram": "@companyhandle",
                                "tiktok": "@companytiktok",
                                "youtube": "@companychannel",
                                "twitter": "@companytwitter",
                                "linkedin": "https://linkedin.com/company/companyname",
                                "profile_picture": "uploads/organizations/123e4567-e89b-12d3-a456-426614174000/logo.jpg",
                                "skill_sets": ["Python", "Leadership"],
                                "job_roles": ["Manager", "Software Engineer"],
                                "is_setup_complete": True,
                                "created_at": "2024-04-04T12:34:56Z",
                                "updated_at": "2024-04-05T10:11:12Z"
                            }
                        }
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - No fields provided for update",
            "content": {
                "application/json": {
                    "example": {"detail": "No profile fields provided for update"}
                }
            },
        },
        401: {
            "description": "Unauthorized - Invalid or missing token",
            "content": {
                "application/json": {"example": {"detail": "Unauthorized"}}
            },
        },
        404: {
            "description": "User or Organization not found",
            "content": {
                "application/json": {"example": {"detail": "User or Organization not found"}}
            },
        },
        500: {
            "description": "Internal Server Error",
            "content": {
                "application/json": {"example": {"detail": "Error during updating profile"}}
            },
        },
    },
)
async def update_profile(
    profile_data: UserProfileUpdate,
    current_user: Dict[str, Any] = Depends(BaseAuthGuard()),
):
    try:
        if current_user["registration_type"] == RegistrationType.individual:
            response = await user_service.update_user_profile(
                user_id=current_user["user_id"],
                profile_data=profile_data,
            )
        elif current_user["registration_type"] == RegistrationType.organization:
            response = await user_service.update_organization_profile(
                org_id=current_user["user_id"],
                profile_data=profile_data,
            )
        else:
            raise HTTPException(status_code=403, detail="Invalid registration type for profile update")

        return JSONResponse(content=response)
    except ValidationError as ve:
        # Catch Pydantic validation errors
        raise HTTPException(status_code=422, detail=ve.errors())
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@user_router.get(
    "/entity-resources",
    summary="Get Entity Resources",
    description="""
    This endpoint retrieves all resources for a user or organization in a single request.

    - Requires a valid JWT Bearer token.
    - For organizations, returns job descriptions, skill sets, and job roles.
    - For individual users, returns job descriptions.
    """,
    responses={
        200: {
            "description": "Resources retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Resources retrieved successfully",
                        "data": {
                            "job_descriptions": {
                                "jd_id1": {
                                    "name": "Software Engineer",
                                    "link": "https://example.com/jd/software-engineer.pdf",
                                },
                                "jd_id2": {
                                    "name": "Product Manager",
                                    "link": "https://example.com/jd/product-manager.pdf",
                                },
                            },
                            "skill_sets": ["Python", "JavaScript"],  # Only for organizations
                            "job_roles": ["Developer", "Manager"],   # Only for organizations
                        },
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Not authenticated",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {
                "application/json": {"example": {"detail": "Error fetching resources"}}
            },
        },
    },
)
async def get_entity_resources(
    current_user: dict = Depends(roles_required([RegistrationType.organization, RegistrationType.individual])),
):
    """
    Handles retrieving all resources (job descriptions, and for organizations: skill sets and job roles)
    in a single request.
    """
    try:
        entity_id = current_user["user_id"]
        entity_type = current_user["registration_type"]
        response = user_service.get_entity_resources(entity_id, entity_type)
        return JSONResponse(content=response)
    except Exception as e:
        print(f"Error fetching resources: {str(e)}")
        raise HTTPException(
            status_code=getattr(e, "status_code", 500),
            detail="Error fetching resources",
        )


@user_router.put(
    "/update-individual-job-descriptions",
    summary="Update individual's job descriptions",
    description="""
    This endpoint allows individual users to update their job descriptions.

    - Only authenticated individual users can access this.
    - Requires a valid JWT Bearer token.
    - Accepts a list of job descriptions in the `standalone_job_descriptions` field.
    - Job descriptions can be provided either as an S3 link (`jd_link`) or as direct text content (`jd_text`).
    - When providing job descriptions as text, no file upload is required.
    - Job descriptions are stored with a timestamp-based ID for better organization.
    - These job descriptions can be used when scheduling interviews.
    """,
    responses={
        200: {
            "description": "Update successful",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "User job descriptions updated successfully",
                        "data": {
                            "standalone_job_descriptions": {
                                "jd_20250520_123456": {
                                    "name": "Software Engineer",
                                    "link": "https://example.com/jd/software-engineer.pdf",
                                },
                                "jd_20250520_123457": {
                                    "name": "Product Manager",
                                    "description": "Job description text content...",
                                },
                            }
                        },
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid input",
            "content": {
                "application/json": {"example": {"detail": "No job descriptions provided"}}
            },
        },
        401: {
            "description": "Unauthorized - Invalid or missing token",
            "content": {"application/json": {"example": {"detail": "Not authenticated"}}},
        },
        403: {
            "description": "Forbidden - Not an individual user",
            "content": {
                "application/json": {
                    "example": {"detail": "Only individual users can access this endpoint"}
                }
            },
        },
        500: {
            "description": "Internal Server Error",
            "content": {
                "application/json": {"example": {"detail": "Error updating job descriptions"}}
            },
        },
    },
)
async def update_individual_job_descriptions(
    job_descriptions: UserUpdateJobDescriptionsSchema,
    current_user: dict = Depends(roles_required([RegistrationType.individual])),
):
    """
    Handles updating job descriptions for individual users.
    """
    try:
        response = await user_service.update_user_job_descriptions(job_descriptions, current_user)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@user_router.put(
    "/update-org-setup",
    summary="Update organization's skill sets, job roles, and job descriptions",
    description="""
    This endpoint allows organizations to update their skill sets, job roles, and job descriptions.

    - Only authenticated organizations can access this.
    - Requires a valid JWT Bearer token.
    - Accepts optional fields: `skill_sets`, `job_roles`, and `standalone_job_descriptions`.
    - If both `skill_sets` and `job_roles` are provided, the organization's setup is marked as complete.
    - Standalone job descriptions are not linked to any skill set or job role.
    - Standalone job descriptions can be selected directly when scheduling interviews.
    - Job descriptions can be provided either as an S3 link (`jd_link`) or as direct text content (`jd_text`).
    - When providing job descriptions as text, no file upload is required.
    - Job descriptions are stored with a timestamp-based ID for better organization.
    """,
    responses={
        200: {
            "description": "Update successful",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "Organization profile updated successfully",
                        "data": {
                            "skill_sets": ["Python", "Leadership"],
                            "job_roles": ["Manager", "Software Engineer"],
                            "standalone_job_descriptions": {
                                "20230615_123045_abc12345": {
                                    "name": "Software Engineer",
                                    "link": "https://example.com/jd/software-engineer.pdf",
                                    "description": "Job description text extracted from the PDF...",
                                    "created_at": "2023-06-15T12:30:45.123456"
                                },
                                "20230615_124530_def67890": {
                                    "name": "Product Manager",
                                    "description": "We are looking for an experienced Product Manager...",
                                    "link": None,
                                    "created_at": "2023-06-15T12:45:30.654321"
                                },
                            },
                        },
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Missing fields for update",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "At least one of 'skill_sets', 'job_roles', or 'standalone_job_descriptions' must be provided."
                    }
                }
            },
        },
        403: {
            "description": "Forbidden - Organization only",
            "content": {
                "application/json": {
                    "example": {"detail": "Access denied. Only organizations are allowed."}
                }
            },
        },
        404: {
            "description": "Organization not found",
            "content": {
                "application/json": {
                    "example": {"detail": "The requested organization was not found."}
                }
            },
        },
        422: {
            "description": "Validation Error - Invalid input format",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "skill_sets"],
                                "msg": "Skill sets must be a list of strings",
                                "type": "type_error.list",
                            }
                        ]
                    }
                }
            },
        },
    },
)
async def update_org_skills_and_jobs(
    skills_and_jobs: OrganizationUpdateSkillsJobsSchema,
    current_instance: dict = Depends(roles_required([RegistrationType.organization])),
):

    """
    Handles the update of skills and jobs

    """
    try:
        # Call the existing service to process the auth code
        response = await user_service.update_skills_and_jobs(skills_and_jobs, current_instance)
        return JSONResponse(content=response)
    except Exception as e:
        # Raise HTTPException with the status code and message from the custom exception
        raise HTTPException(status_code=getattr(e, "status_code", 400), detail=str(e))


@user_router.get(
    "/profiles",
    response_model=dict,
    summary="Get Paginated User Profiles",
    description="""
    This endpoint retrieves a paginated list of user profiles with optional filtering and sorting.
    
    - Requires authentication with a valid JWT Bearer token.
    - Returns public profile information for all active users.
    - Use `skip` and `limit` for pagination.
    - Placeholder parameters for future filtering and sorting features.
    
    **Query Parameters:**
    - `skip`: Number of records to skip (default: 0)
    - `limit`: Maximum number of records to return (default: 20, max: 100)
    - `top_profiles`: Placeholder for top profiles filtering (default: false)
    - `skill_filter`: Placeholder for skill-based filtering
    - `sort_by`: Placeholder for sorting options (default: 'created_at')
    
    **Response includes:**
    - User profile data (public fields only)
    - Pagination metadata (total count, pages, current page info)
    """,
    responses={
        200: {
            "description": "User profiles retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "User profiles retrieved successfully",
                        "data": [
                            {
                                "id": "123e4567-e89b-12d3-a456-426614174000",
                                "email": "<EMAIL>",
                                "full_name": "John Doe",
                                "position": "Product Design Lead",
                                "location": "London, UK",
                                "bio": "Experienced product designer with 5+ years in the industry.",
                                "website": "https://myportfolio.com",
                                "profile_picture": "uploads/123e4567-e89b-12d3-a456-426614174000/abc123_profile.jpg",
                                "instagram": "@designerhandle",
                                "tiktok": "@designertiktok",
                                "youtube": "@designerchannel",
                                "twitter": "@designertwitter",
                                "linkedin": "https://linkedin.com/in/designer",
                                "resume_link": "https://bucket-name.s3.us-east-1.amazonaws.com/uploads/user-id/resume.pdf",
                                "skill_sets": ["Blockchain", "AI", "Python", "JavaScript"],
                                "rating": 4.5,
                                "created_at": "2024-04-04T12:34:56Z",
                            }
                        ],
                        "pagination": {
                            "total_users": 150,
                            "total_pages": 15,
                            "current_page": 3,
                            "current_page_length": 10,
                            "page_size": 10,
                        },
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid pagination parameters",
            "content": {
                "application/json": {
                    "examples": {
                        "negative_skip": {
                            "summary": "Negative skip parameter",
                            "value": {"detail": "Skip parameter cannot be negative"},
                        },
                        "invalid_limit": {
                            "summary": "Invalid limit parameter",
                            "value": {"detail": "Limit must be between 1 and 100"},
                        },
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Invalid or missing token",
            "content": {"application/json": {"example": {"detail": "Unauthorized"}}},
        },
        422: {
            "description": "Validation Error - Invalid query parameters",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["query", "limit"],
                                "msg": "ensure this value is less than or equal to 100",
                                "type": "value_error.number.not_le",
                                "ctx": {"limit_value": 100},
                            }
                        ]
                    }
                }
            },
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {
                "application/json": {"example": {"detail": "Error retrieving user profiles"}}
            },
        },
    },
)
async def get_paginated_user_profiles(
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(20, description="Limit to N records"),
    top_profiles: bool = Query(False, description="Filter top profiles (placeholder)"),
    skill_filter: Optional[str] = Query(None, description="Filter by skills (placeholder)"),
    sort_by: str = Query('created_at', description="Sort by field (placeholder)"),
):
    """
    Get paginated user profiles with optional filtering and sorting.
    
    This endpoint provides a paginated list of user profiles with comprehensive
    metadata. It includes placeholder parameters for future filtering and sorting features.
    """
    try:
        # Validate pagination parameters
        if skip < 0:
            raise HTTPException(status_code=400, detail="Skip parameter cannot be negative")
        
        if limit < 1 or limit > 100:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 100")
        
        # Call the service method
        response = user_service.get_paginated_user_profiles(
            skip=skip,
            limit=limit,
            top_profiles=top_profiles,
            skill_filter=skill_filter,
            sort_by=sort_by
        )
        
        return JSONResponse(content=response.model_dump())
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Handle any other exceptions
        raise HTTPException(
            status_code=getattr(e, "status_code", 500),
            detail=f"Error retrieving user profiles: {str(e)}"
        )


@user_router.get(
    "/organizations",
    response_model=dict,
    summary="Get Paginated Organizations with Advanced Filtering",
    description="""
    This endpoint retrieves a comprehensive list of organizations with advanced filtering capabilities.
    
    - Requires authentication with a valid JWT Bearer token.
    - Returns public organization information for all active organizations.
    - Use `skip` and `limit` for pagination.
    - Supports multiple filtering options including top10, recommended, skill-based, and location-based filters.
    - Includes sorting options and extensible filter system.
    
    **Query Parameters:**
    - `skip`: Number of records to skip (default: 0)
    - `limit`: Maximum number of records to return (default: 20, max: 100)
    - `top10`: Filter for top 10 organizations by rating (default: false)
    - `recommended`: Filter for recommended organizations (default: false)
    - `skill_filter`: Filter by skill sets (placeholder for future implementation)
    - `location_filter`: Filter by location (placeholder for future implementation)
    - `sort_by`: Sorting options - 'created_at', 'name', 'rating' (default: 'created_at')
    
    **Response includes:**
    - Organization profile data (public fields only)
    - Organization metrics (rating, total interviews)
    - Pagination metadata (total count, pages, current page info)
    """,
    responses={
        200: {
            "description": "Organizations retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Organizations retrieved successfully",
                        "data": [
                            {
                                "id": "123e4567-e89b-12d3-a456-426614174000",
                                "email": "<EMAIL>",
                                "organization_name": "Acme Corp",
                                "position": "Tech Company",
                                "location": "San Francisco, CA",
                                "bio": "Leading technology company specializing in AI solutions.",
                                "website": "https://company.com",
                                "profile_picture": "uploads/organizations/123e4567-e89b-12d3-a456-426614174000/logo.jpg",
                                "instagram": "@companyhandle",
                                "tiktok": "@companytiktok",
                                "youtube": "@companychannel",
                                "twitter": "@companytwitter",
                                "linkedin": "https://linkedin.com/company/companyname",
                                "total_interviews": 125,
                                "created_at": "2024-04-04T12:34:56Z"
                            }
                        ],
                        "pagination": {
                            "total_organizations": 75,
                            "total_pages": 8,
                            "current_page": 2,
                            "current_page_length": 10,
                            "page_size": 10
                        }
                    }
                }
            },
        },
        400: {
            "description": "Bad Request - Invalid pagination or filter parameters",
            "content": {
                "application/json": {
                    "examples": {
                        "negative_skip": {
                            "summary": "Negative skip parameter",
                            "value": {"detail": "Skip parameter cannot be negative"}
                        },
                        "invalid_limit": {
                            "summary": "Invalid limit parameter",
                            "value": {"detail": "Limit must be between 1 and 100"}
                        },
                        "invalid_sort": {
                            "summary": "Invalid sort parameter",
                            "value": {"detail": "Sort by must be one of: created_at, name, rating"}
                        }
                    }
                }
            },
        },
        401: {
            "description": "Unauthorized - Invalid or missing token",
            "content": {
                "application/json": {"example": {"detail": "Unauthorized"}}
            },
        },
        422: {
            "description": "Validation Error - Invalid query parameters",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["query", "limit"],
                                "msg": "ensure this value is less than or equal to 100",
                                "type": "value_error.number.not_le",
                                "ctx": {"limit_value": 100}
                            }
                        ]
                    }
                }
            },
        },
        500: {
            "description": "Internal Server Error - Database or server issue",
            "content": {
                "application/json": {"example": {"detail": "Error retrieving organizations"}}
            },
        },
    },
)
async def get_paginated_organizations(
    skip: int = Query(0, description="Skip N records"),
    limit: int = Query(20, description="Limit to N records"),
    top10: bool = Query(False, description="Filter top 10 organizations by rating"),
    recommended: bool = Query(False, description="Filter recommended organizations"),
    skill_filter: Optional[str] = Query(None, description="Filter by skill sets"),
    location_filter: Optional[str] = Query(None, description="Filter by location"),
    sort_by: str = Query('created_at', description="Sort by field: created_at, name, rating"),
):
    """
    Get paginated organizations with advanced filtering capabilities.
    
    This endpoint provides a comprehensive list of organizations with multiple
    filtering options, sorting capabilities, and pagination support.
    """
    try:
        # Validate pagination parameters
        if skip < 0:
            raise HTTPException(status_code=400, detail="Skip parameter cannot be negative")
        
        if limit < 1 or limit > 100:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 100")
        
        # Validate sort_by parameter
        valid_sort_options = ['created_at', 'name', 'rating']
        if sort_by not in valid_sort_options:
            raise HTTPException(
                status_code=400,
                detail=f"Sort by must be one of: {', '.join(valid_sort_options)}"
            )
        
        # Call the service method
        response = user_service.get_paginated_organizations(
            skip=skip,
            limit=limit,
            top10=top10,
            recommended=recommended,
            skill_filter=skill_filter,
            location_filter=location_filter,
            sort_by=sort_by
        )
        
        return JSONResponse(content=response.model_dump())
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Handle any other exceptions
        raise HTTPException(
            status_code=getattr(e, "status_code", 500),
            detail=f"Error retrieving organizations: {str(e)}"
        )


# --- Public Routes (No Authentication Required) ---

@user_router.get(
    "/public/user/{user_id}",
    summary="Get Individual User Public Profile (No Authentication Required)",
    description="Retrieve public profile information for a specific user without authentication. Returns public information only, excluding sensitive data.",
)
async def get_public_user_profile(user_id: str):
    """Get public profile information for a specific user without authentication."""
    try:
        response = user_service.get_public_user_profile(user_id)
        return JSONResponse(content=response)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 500), detail=str(e))


@user_router.get(
    "/public/organization/{org_id}",
    summary="Get Individual Organization Public Profile (No Authentication Required)",
    description="Retrieve public profile information for a specific organization without authentication. Returns public information only, excluding sensitive data.",
)
async def get_public_organization_profile(org_id: str):
    """Get public profile information for a specific organization without authentication."""
    try:
        response = user_service.get_public_organization_profile(org_id)
        return JSONResponse(content=response)
    except Exception as e:
        raise HTTPException(status_code=getattr(e, "status_code", 500), detail=str(e))


# --- Email Verification Routes (moved from email_verification_routes.py) ---

@user_router.post(
    "/verify-email-otp",
    summary="Verify Email OTP",
    description="""
    Verify the OTP code sent to the user's email address.
    Upon successful verification, the user's email will be marked as verified.
    """
)
async def verify_email_otp(
    request: VerifyOTPRequest,
    db: Session = Depends(get_db)
):
    """
    Verify OTP and mark email as verified

    Args:
        request: VerifyOTPRequest containing email and otp_code
        db: Database session

    Returns:
        OTPVerificationResponse: Result of the verification operation

    Raises:
        HTTPException: If verification fails or user not found
    """
    try:
        # Initialize user service
        user_service = UserService(db)

        # Verify OTP (this also updates user verification status)
        result = await user_service.verify_email_otp(
            email=request.email,
            otp_code=request.otp_code
        )

        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Verification failed: {str(e)}"
        )


@user_router.post(
    "/resend-email-otp",
    response_model=EmailVerificationResponse,
    summary="Resend Email Verification OTP",
    description="""
    Resend the OTP code to the user's email address.
    This endpoint can be used if the user didn't receive the original OTP or if it expired.

    Rate limiting: Only one OTP can be sent per minute to prevent spam.
    """
)
async def resend_verification_otp(
    request: ResendOTPRequest,
    db: Session = Depends(get_db)
) -> EmailVerificationResponse:
    """
    Resend OTP for email verification

    Args:
        request: ResendOTPRequest containing email
        db: Database session

    Returns:
        EmailVerificationResponse: Result of the OTP resending operation

    Raises:
        HTTPException: If resending fails or rate limit exceeded
    """
    try:
        # Initialize user service
        user_service = UserService(db)

        # Resend OTP
        result = await user_service.resend_verification_otp(
            email=request.email
        )

        if result["success"]:
            return EmailVerificationResponse(**result)
        else:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=result["message"]
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resend verification email: {str(e)}"
        )


@user_router.get(
    "/email-verification-status/{email}",
    summary="Get Email Verification Status",
    description="""
    Get the current email verification status for a given email address.
    This endpoint can be used to check if an email is already verified.
    """
)
async def get_verification_status(
    email: str,
    db: Session = Depends(get_db)
):
    """
    Get email verification status

    Args:
        email: Email address to check
        db: Database session

    Returns:
        dict: Verification status information
    """
    try:
        # Initialize user service
        user_service = UserService(db)

        # Get verification status
        result = user_service.get_verification_status(email)

        if "error" in result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result["error"]
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get verification status: {str(e)}"
        )
