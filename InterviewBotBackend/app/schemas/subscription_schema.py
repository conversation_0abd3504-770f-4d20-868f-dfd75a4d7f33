from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from decimal import Decimal


class SubscriptionPlanBase(BaseModel):
    name: str = Field(..., description="Name of the subscription plan")
    description: Optional[str] = Field(None, description="Description of the subscription plan")
    interview_limit: int = Field(..., ge=0, description="Maximum number of interviews allowed")
    price: Optional[Decimal] = Field(default=0.00, ge=0, description="Price of the subscription plan")
    is_active: bool = Field(default=True, description="Whether the plan is active")
    features: Optional[Dict[str, Any]] = Field(None, description="Additional features for the plan")


class SubscriptionPlanCreate(SubscriptionPlanBase):
    pass


class SubscriptionPlanUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Name of the subscription plan")
    description: Optional[str] = Field(None, description="Description of the subscription plan")
    interview_limit: Optional[int] = Field(None, ge=0, description="Maximum number of interviews allowed")
    price: Optional[Decimal] = Field(None, ge=0, description="Price of the subscription plan")
    is_active: Optional[bool] = Field(None, description="Whether the plan is active")
    features: Optional[Dict[str, Any]] = Field(None, description="Additional features for the plan")


class SubscriptionPlanResponse(SubscriptionPlanBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserSubscriptionResponse(BaseModel):
    id: str
    user_id: str
    subscription_plan_id: str
    interviews_used: int
    interviews_remaining: int
    activated_at: datetime
    expires_at: Optional[datetime]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    subscription_plan: SubscriptionPlanResponse

    class Config:
        from_attributes = True


class OrganizationSubscriptionResponse(BaseModel):
    id: str
    organization_id: str
    subscription_plan_id: str
    interviews_used: int
    interviews_remaining: int
    activated_at: datetime
    expires_at: Optional[datetime]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    subscription_plan: SubscriptionPlanResponse

    class Config:
        from_attributes = True


class SubscriptionUsageResponse(BaseModel):
    interviews_used: int
    interviews_remaining: int
    interview_limit: int
    plan_name: str
    is_limit_reached: bool
    can_schedule_interview: bool


class SubscriptionStatsResponse(BaseModel):
    total_users: int
    total_organizations: int
    total_active_subscriptions: int
    total_plans: int
    usage_by_plan: Dict[str, Dict[str, Any]]


class UserSubscriptionCreateRequest(BaseModel):
    user_id: str
    subscription_plan_id: str


class OrganizationSubscriptionCreateRequest(BaseModel):
    organization_id: str
    subscription_plan_id: str


class BulkSubscriptionUpdateRequest(BaseModel):
    user_ids: list[str]
    subscription_plan_id: str


class BulkOrganizationSubscriptionUpdateRequest(BaseModel):
    organization_ids: list[str]
    subscription_plan_id: str