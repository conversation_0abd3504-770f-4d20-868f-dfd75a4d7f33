[tool.poetry]
name = "interview-backend"
version = "0.1.0"
description = "Backend for Interview Management"
authors = ["Shob<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "interview_backend", from = "app"}]

[tool.poetry.dependencies]
python = ">=3.10,<3.12"
bcrypt = "^3.2.0"
python-dotenv = "^1.0.1"
pydantic-settings = "^2.8.1"
pydantic = "^2.10.6"
python-jose = {extras = ["cryptography"], version = "^3.4.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
psycopg2-binary = "^2.9.10"
sqlalchemy = "^2.0.39"
alembic = "^1.13.1"  # Added for database migrations
structlog = "^24.1.0"  # Added for structured logging
redis = "^5.0.1"  # Added for caching support
uvicorn = "^0.34.0"
fastapi = "^0.115.11"
email-validator = "^2.2.0"
python-multipart = "^0.0.20"
requests = "^2.32.3"
boto3 = "^1.37.24"
mcp = "^1.6.0"
pytz = "^2025.2"
pytesseract = "^0.3.13"
pdf2image = "^1.17.0"
livekit = "^1.0.6"
livekit-api = "^1.0.2"
reportlab = "^4.4.0"
fastapi-mail="1.5.0"
razorpay = "^1.4.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.4"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.23.3"
black = "^23.12.1"
isort = "^5.13.2"
mypy = "^1.8.0"
pylint = "^3.0.3"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py310"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pylint.messages_control]
disable = ["C0111", "C0103", "C0330", "C0326"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=app --cov-report=term-missing"
asyncio_mode = "auto"
