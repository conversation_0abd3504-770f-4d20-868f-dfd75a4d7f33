"""
Order Model

This module defines the Order model for tracking Razorpay orders.
Orders represent payment requests created through the Razorpay API.
"""

from datetime import datetime
from sqlalchemy import Column, String, Numeric, DateTime, ForeignKey, JSON, Enum as SQLEnum
from sqlalchemy.orm import relationship
from enum import Enum
from app.models.base import Base


class OrderStatus(str, Enum):
    """Order status enumeration."""
    CREATED = "created"
    ATTEMPTED = "attempted"
    PAID = "paid"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Order(Base):
    """
    Order model for Razorpay orders.
    
    Tracks orders created through Razorpay API for subscription plan purchases.
    Links to plans and contains Razorpay-specific order information.
    """
    __tablename__ = "orders"

    id = Column(String, primary_key=True)
    razorpay_order_id = Column(String, unique=True, nullable=False, index=True)
    
    # Plan and user information
    plan_id = Column(String, ForeignKey("plans.id"), nullable=False)
    user_id = Column(String, nullable=False)  # User who created the order
    user_email = Column(String, nullable=True)  # User email for reference
    
    # Order details
    amount = Column(Numeric(10, 2), nullable=False)  # Amount in currency units
    currency = Column(String, default="USD", nullable=False)
    status = Column(SQLEnum(OrderStatus), default=OrderStatus.CREATED, nullable=False)
    
    # Razorpay specific fields
    razorpay_receipt = Column(String, nullable=True)  # Receipt number for Razorpay
    razorpay_notes = Column(JSON, nullable=True)  # Additional notes for Razorpay
    
    # Payment tracking
    payment_attempts = Column(JSON, nullable=True)  # Track payment attempts
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    paid_at = Column(DateTime, nullable=True)  # When payment was completed
    expires_at = Column(DateTime, nullable=True)  # When order expires

    # Relationships
    plan = relationship("Plan", back_populates="orders")
    transactions = relationship("Transaction", back_populates="order", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Order(id='{self.id}', razorpay_order_id='{self.razorpay_order_id}', status='{self.status}')>"

    def to_dict(self) -> dict:
        """Convert order to dictionary representation."""
        return {
            "id": self.id,
            "razorpay_order_id": self.razorpay_order_id,
            "plan_id": self.plan_id,
            "user_id": self.user_id,
            "user_email": self.user_email,
            "amount": float(self.amount) if self.amount else 0.0,
            "currency": self.currency,
            "status": self.status.value if self.status else None,
            "razorpay_receipt": self.razorpay_receipt,
            "razorpay_notes": self.razorpay_notes,
            "payment_attempts": self.payment_attempts,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "paid_at": self.paid_at.isoformat() if self.paid_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
        }
