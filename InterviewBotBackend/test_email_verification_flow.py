#!/usr/bin/env python3
"""
Test Email Verification Flow
Tests the complete signup → OTP → verification → login flow
"""

import asyncio
import requests
import json
from datetime import datetime

class EmailVerificationTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.test_email = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com"
        self.test_password = "TestPassword123!"
        self.test_name = "Test User"
        
    def test_api_endpoint(self, method, endpoint, data=None, headers=None):
        """Test an API endpoint and return the response"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == "POST":
                response = requests.post(url, json=data, headers=headers)
            elif method.upper() == "GET":
                response = requests.get(url, headers=headers)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            print(f"\n{method} {endpoint}")
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text[:500]}...")
            
            return response
            
        except Exception as e:
            print(f"Error testing {method} {endpoint}: {e}")
            return None

    def test_user_registration(self):
        """Test user registration (should create user with is_email_verified=False)"""
        print("\n" + "="*60)
        print("TESTING USER REGISTRATION")
        print("="*60)
        
        registration_data = {
            "email": self.test_email,
            "password": self.test_password,
            "full_name": self.test_name,
            "registration_type": "individual"
        }
        
        response = self.test_api_endpoint("POST", "/api/v1/auth/register", registration_data)
        
        if response and response.status_code == 200:
            print("✅ User registration successful")
            return True
        else:
            print("❌ User registration failed")
            return False

    def test_send_verification_otp(self):
        """Test sending verification OTP"""
        print("\n" + "="*60)
        print("TESTING SEND VERIFICATION OTP")
        print("="*60)
        
        otp_data = {
            "email": self.test_email,
            "user_name": self.test_name,
            "user_type": "candidate"
        }
        
        response = self.test_api_endpoint("POST", "/api/v1/email-verification/send-otp", otp_data)
        
        if response and response.status_code == 200:
            print("✅ OTP sending successful")
            return True
        else:
            print("❌ OTP sending failed")
            return False

    def test_verify_otp(self, otp_code="123456"):
        """Test OTP verification"""
        print("\n" + "="*60)
        print("TESTING OTP VERIFICATION")
        print("="*60)
        
        verify_data = {
            "email": self.test_email,
            "otp_code": otp_code
        }
        
        response = self.test_api_endpoint("POST", "/api/v1/email-verification/verify-otp", verify_data)
        
        if response and response.status_code == 200:
            print("✅ OTP verification successful")
            return True
        else:
            print("❌ OTP verification failed")
            return False

    def test_login_before_verification(self):
        """Test login before email verification (should fail)"""
        print("\n" + "="*60)
        print("TESTING LOGIN BEFORE EMAIL VERIFICATION")
        print("="*60)
        
        login_data = {
            "username": self.test_email,
            "password": self.test_password
        }
        
        # Use form data for OAuth2 login
        response = requests.post(
            f"{self.base_url}/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        print(f"\nPOST /api/v1/auth/login")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        
        if response.status_code == 401 or "not verified" in response.text.lower():
            print("✅ Login correctly blocked for unverified email")
            return True
        else:
            print("❌ Login should be blocked for unverified email")
            return False

    def test_login_after_verification(self):
        """Test login after email verification (should succeed)"""
        print("\n" + "="*60)
        print("TESTING LOGIN AFTER EMAIL VERIFICATION")
        print("="*60)
        
        login_data = {
            "username": self.test_email,
            "password": self.test_password
        }
        
        # Use form data for OAuth2 login
        response = requests.post(
            f"{self.base_url}/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        print(f"\nPOST /api/v1/auth/login")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:500]}...")
        
        if response.status_code == 200:
            print("✅ Login successful after email verification")
            return True
        else:
            print("❌ Login failed after email verification")
            return False

    def test_verification_status(self):
        """Test getting verification status"""
        print("\n" + "="*60)
        print("TESTING VERIFICATION STATUS")
        print("="*60)
        
        response = self.test_api_endpoint("GET", f"/api/v1/email-verification/status/{self.test_email}")
        
        if response and response.status_code == 200:
            print("✅ Verification status check successful")
            return True
        else:
            print("❌ Verification status check failed")
            return False

    def run_complete_test(self):
        """Run the complete email verification flow test"""
        print("🚀 STARTING EMAIL VERIFICATION FLOW TEST")
        print("="*80)
        print(f"Test Email: {self.test_email}")
        print(f"Test Name: {self.test_name}")
        print("="*80)
        
        results = []
        
        # Test 1: User Registration
        results.append(("User Registration", self.test_user_registration()))
        
        # Test 2: Send Verification OTP
        results.append(("Send Verification OTP", self.test_send_verification_otp()))
        
        # Test 3: Login Before Verification (should fail)
        results.append(("Login Before Verification", self.test_login_before_verification()))
        
        # Test 4: Verify OTP (using a dummy OTP for testing)
        # Note: In a real test, you'd need to get the actual OTP from the database or email
        print("\n⚠️  Note: Using dummy OTP '123456' for testing. In production, get actual OTP from database.")
        results.append(("OTP Verification", self.test_verify_otp("123456")))
        
        # Test 5: Login After Verification (should succeed)
        results.append(("Login After Verification", self.test_login_after_verification()))
        
        # Test 6: Check Verification Status
        results.append(("Verification Status", self.test_verification_status()))
        
        # Summary
        print("\n" + "="*80)
        print("TEST RESULTS SUMMARY")
        print("="*80)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<30} {status}")
            if result:
                passed += 1
        
        print("-" * 80)
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 ALL TESTS PASSED! Email verification flow is working correctly.")
        else:
            print(f"\n⚠️  {total - passed} tests failed. Please check the implementation.")
        
        return passed == total

def main():
    """Main function"""
    print("Email Verification Flow Tester")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/docs")
        if response.status_code == 200:
            print("✅ Server is running at http://localhost:8000")
        else:
            print("⚠️  Server responded but may not be fully ready")
    except Exception as e:
        print("❌ Server is not running. Please start the server first.")
        print("Run: uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return
    
    # Run tests
    tester = EmailVerificationTester()
    success = tester.run_complete_test()
    
    if success:
        print("\n🎯 Email verification feature is ready for production!")
    else:
        print("\n🔧 Some issues need to be fixed before deployment.")

if __name__ == "__main__":
    main()
