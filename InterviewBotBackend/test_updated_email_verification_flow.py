#!/usr/bin/env python3
"""
Updated Email Verification Flow Tester
Tests the new email verification flow where:
1. OTP is sent automatically during signup
2. No separate send-otp endpoint
3. Resend OTP has 1.5 minute cooldown
4. OTP codes are encrypted
5. Business logic is in service files
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com"
TEST_NAME = "Test User"
TEST_PASSWORD = "TestPassword123!"

def print_header(title: str):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"{title}")
    print("="*60)

def print_section(title: str):
    """Print a formatted section header"""
    print(f"\n{'='*40}")
    print(f"{title}")
    print("="*40)

def make_request(method: str, endpoint: str, data: Dict[Any, Any] = None) -> tuple:
    """Make HTTP request and return status code and response"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url)
        elif method.upper() == "POST":
            response = requests.post(url, json=data)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        return response.status_code, response.json()
    except requests.exceptions.RequestException as e:
        return 0, {"error": f"Request failed: {str(e)}"}
    except json.JSONDecodeError:
        return response.status_code, {"error": "Invalid JSON response"}

def test_server_health():
    """Test if server is running"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running at", BASE_URL)
            return True
        else:
            print("❌ Server health check failed")
            return False
    except:
        print("❌ Server is not accessible at", BASE_URL)
        return False

def test_user_registration():
    """Test user registration (should automatically send OTP)"""
    print_section("TESTING USER REGISTRATION WITH AUTO OTP")
    
    registration_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "full_name": TEST_NAME,
        "registration_type": "individual"
    }
    
    print(f"POST /api/v1/auth/register")
    status, response = make_request("POST", "/api/v1/auth/register", registration_data)
    print(f"Status: {status}")
    print(f"Response: {json.dumps(response, indent=2)[:200]}...")
    
    if status == 200 and response.get("success"):
        print("✅ User registration successful (OTP should be sent automatically)")
        return True
    else:
        print("❌ User registration failed")
        return False

def test_login_before_verification():
    """Test login before email verification (should be blocked)"""
    print_section("TESTING LOGIN BEFORE EMAIL VERIFICATION")
    
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    print(f"POST /api/v1/auth/login")
    status, response = make_request("POST", "/api/v1/auth/login", login_data)
    print(f"Status: {status}")
    print(f"Response: {json.dumps(response, indent=2)[:200]}...")
    
    if status == 401 and "not verified" in response.get("detail", "").lower():
        print("✅ Login correctly blocked for unverified email")
        return True
    else:
        print("❌ Login should be blocked for unverified email")
        return False

def test_resend_otp_immediate():
    """Test immediate resend OTP (should be blocked by cooldown)"""
    print_section("TESTING IMMEDIATE RESEND OTP (SHOULD BE BLOCKED)")
    
    resend_data = {
        "email": TEST_EMAIL,
        "user_name": TEST_NAME,
        "user_type": "candidate"
    }
    
    print(f"POST /api/v1/email-verification/resend-otp")
    status, response = make_request("POST", "/api/v1/email-verification/resend-otp", resend_data)
    print(f"Status: {status}")
    print(f"Response: {json.dumps(response, indent=2)[:200]}...")
    
    if status == 429 and "wait" in response.get("detail", "").lower():
        print("✅ Resend OTP correctly blocked by cooldown")
        return True
    else:
        print("❌ Resend OTP should be blocked by cooldown")
        return False

def test_otp_verification():
    """Test OTP verification with dummy OTP"""
    print_section("TESTING OTP VERIFICATION")
    
    # Use dummy OTP for testing
    dummy_otp = "123456"
    print(f"⚠️  Using dummy OTP '{dummy_otp}' for testing")
    print("   In production, get actual OTP from database or email")
    
    verify_data = {
        "email": TEST_EMAIL,
        "otp_code": dummy_otp
    }
    
    print(f"POST /api/v1/email-verification/verify-otp")
    status, response = make_request("POST", "/api/v1/email-verification/verify-otp", verify_data)
    print(f"Status: {status}")
    print(f"Response: {json.dumps(response, indent=2)[:200]}...")
    
    if status == 200 and response.get("success"):
        print("✅ OTP verification successful")
        return True
    elif status == 400:
        print("⚠️  OTP verification failed (expected with dummy OTP)")
        return False
    else:
        print("❌ OTP verification failed")
        return False

def test_verification_status():
    """Test verification status endpoint"""
    print_section("TESTING VERIFICATION STATUS")
    
    print(f"GET /api/v1/email-verification/status/{TEST_EMAIL}")
    status, response = make_request("GET", f"/api/v1/email-verification/status/{TEST_EMAIL}")
    print(f"Status: {status}")
    print(f"Response: {json.dumps(response, indent=2)[:200]}...")
    
    if status == 200 and "is_verified" in response:
        print("✅ Verification status retrieved successfully")
        return True
    else:
        print("❌ Verification status check failed")
        return False

def test_login_after_verification():
    """Test login after email verification"""
    print_section("TESTING LOGIN AFTER EMAIL VERIFICATION")
    
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    print(f"POST /api/v1/auth/login")
    status, response = make_request("POST", "/api/v1/auth/login", login_data)
    print(f"Status: {status}")
    print(f"Response: {json.dumps(response, indent=2)[:200]}...")
    
    if status == 200 and response.get("success"):
        print("✅ Login successful after email verification")
        return True
    else:
        print("❌ Login failed after email verification")
        return False

def main():
    """Run all tests"""
    print_header("Updated Email Verification Flow Tester")
    print(f"Test Email: {TEST_EMAIL}")
    print(f"Test Name: {TEST_NAME}")
    print("="*60)
    
    # Check server health
    if not test_server_health():
        print("\n❌ Cannot proceed - server is not running")
        return
    
    print("\n🚀 STARTING UPDATED EMAIL VERIFICATION FLOW TEST")
    print("="*80)
    
    # Track test results
    tests = []
    
    # Test 1: User Registration (auto sends OTP)
    tests.append(("User Registration (Auto OTP)", test_user_registration()))
    
    # Test 2: Login before verification (should be blocked)
    tests.append(("Login Before Verification", test_login_before_verification()))
    
    # Test 3: Immediate resend OTP (should be blocked by cooldown)
    tests.append(("Immediate Resend OTP (Blocked)", test_resend_otp_immediate()))
    
    # Test 4: OTP verification
    tests.append(("OTP Verification", test_otp_verification()))
    
    # Test 5: Verification status
    tests.append(("Verification Status", test_verification_status()))
    
    # Test 6: Login after verification
    tests.append(("Login After Verification", test_login_after_verification()))
    
    # Print summary
    print_header("TEST RESULTS SUMMARY")
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Email verification flow is working correctly.")
    elif passed > total // 2:
        print(f"\n⚠️  {total - passed} tests failed. Some issues need to be fixed.")
    else:
        print(f"\n🔧 {total - passed} tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
