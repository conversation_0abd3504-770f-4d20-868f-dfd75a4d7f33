"""
Payment Plan Model

This module defines the Plan model for subscription plans in the payment system.
Plans define the pricing and features available for different subscription tiers.
"""

from datetime import datetime
from sqlalchemy import Column, String, Numeric, Boolean, DateTime, Integer, JSON
from sqlalchemy.orm import relationship
from app.models.base import Base


class Plan(Base):
    """
    Plan model for subscription plans.
    
    Defines the available subscription plans with pricing, features, and limits.
    Used by the Razorpay integration for creating orders and managing subscriptions.
    """
    __tablename__ = "plans"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False, unique=True)
    description = Column(String, nullable=True)
    amount = Column(Numeric(10, 2), nullable=False)  # Amount in currency units (e.g., dollars)
    currency = Column(String, default="USD", nullable=False)
    interval = Column(String, default="monthly", nullable=False)  # monthly, yearly, one-time
    interval_count = Column(Integer, default=1, nullable=False)  # Number of intervals
    
    # Plan features and limits
    features = Column(JSON, nullable=True)  # JSON object with plan features
    interview_limit = Column(Integer, nullable=True)  # Number of interviews allowed
    
    # Plan status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    orders = relationship("Order", back_populates="plan", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Plan(id='{self.id}', name='{self.name}', amount={self.amount})>"

    def to_dict(self) -> dict:
        """Convert plan to dictionary representation."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "amount": float(self.amount) if self.amount else 0.0,
            "currency": self.currency,
            "interval": self.interval,
            "interval_count": self.interval_count,
            "features": self.features,
            "interview_limit": self.interview_limit,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
