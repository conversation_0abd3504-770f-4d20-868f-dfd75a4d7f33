"""
Payment Schemas

This module defines Pydantic schemas for payment-related operations.
Includes schemas for plans, orders, transactions, webhooks, and audit logs.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator
from enum import Enum


# Plan Schemas
class PlanBase(BaseModel):
    """Base plan schema with common fields."""
    name: str = Field(..., description="Plan name")
    description: Optional[str] = Field(None, description="Plan description")
    amount: Decimal = Field(..., ge=0, description="Plan amount")
    currency: str = Field(default="USD", description="Currency code")
    interval: str = Field(default="monthly", description="Billing interval")
    interval_count: int = Field(default=1, ge=1, description="Number of intervals")
    features: Optional[Dict[str, Any]] = Field(None, description="Plan features")
    interview_limit: Optional[int] = Field(None, ge=0, description="Interview limit")
    is_active: bool = Field(default=True, description="Plan active status")


class PlanCreate(PlanBase):
    """Schema for creating a new plan."""
    pass


class PlanUpdate(BaseModel):
    """Schema for updating an existing plan."""
    name: Optional[str] = Field(None, description="Plan name")
    description: Optional[str] = Field(None, description="Plan description")
    amount: Optional[Decimal] = Field(None, ge=0, description="Plan amount")
    currency: Optional[str] = Field(None, description="Currency code")
    interval: Optional[str] = Field(None, description="Billing interval")
    interval_count: Optional[int] = Field(None, ge=1, description="Number of intervals")
    features: Optional[Dict[str, Any]] = Field(None, description="Plan features")
    interview_limit: Optional[int] = Field(None, ge=0, description="Interview limit")
    is_active: Optional[bool] = Field(None, description="Plan active status")


class PlanResponse(PlanBase):
    """Schema for plan response."""
    id: str = Field(..., description="Plan ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True


# Order Schemas
class OrderBase(BaseModel):
    """Base order schema with common fields."""
    plan_id: str = Field(..., description="Plan ID")
    user_email: Optional[str] = Field(None, description="User email")
    razorpay_notes: Optional[Dict[str, Any]] = Field(None, description="Razorpay notes")


class OrderCreate(OrderBase):
    """Schema for creating a new order."""
    pass


class OrderResponse(BaseModel):
    """Schema for order response."""
    id: str = Field(..., description="Order ID")
    razorpay_order_id: str = Field(..., description="Razorpay order ID")
    plan_id: str = Field(..., description="Plan ID")
    user_id: str = Field(..., description="User ID")
    user_email: Optional[str] = Field(None, description="User email")
    amount: Decimal = Field(..., description="Order amount")
    currency: str = Field(..., description="Currency code")
    status: str = Field(..., description="Order status")
    razorpay_receipt: Optional[str] = Field(None, description="Razorpay receipt")
    razorpay_notes: Optional[Dict[str, Any]] = Field(None, description="Razorpay notes")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    paid_at: Optional[datetime] = Field(None, description="Payment timestamp")
    expires_at: Optional[datetime] = Field(None, description="Expiration timestamp")

    class Config:
        from_attributes = True


# Transaction Schemas
class TransactionResponse(BaseModel):
    """Schema for transaction response."""
    id: str = Field(..., description="Transaction ID")
    razorpay_payment_id: Optional[str] = Field(None, description="Razorpay payment ID")
    razorpay_refund_id: Optional[str] = Field(None, description="Razorpay refund ID")
    order_id: str = Field(..., description="Order ID")
    amount: Decimal = Field(..., description="Transaction amount")
    currency: str = Field(..., description="Currency code")
    status: str = Field(..., description="Transaction status")
    transaction_type: str = Field(..., description="Transaction type")
    payment_method: Optional[str] = Field(None, description="Payment method")
    payment_method_details: Optional[Dict[str, Any]] = Field(None, description="Payment method details")
    razorpay_fee: Optional[Decimal] = Field(None, description="Razorpay fee")
    razorpay_tax: Optional[Decimal] = Field(None, description="Razorpay tax")
    error_code: Optional[str] = Field(None, description="Error code")
    error_description: Optional[str] = Field(None, description="Error description")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    processed_at: Optional[datetime] = Field(None, description="Processing timestamp")

    class Config:
        from_attributes = True


# Payment Verification Schema
class PaymentVerification(BaseModel):
    """Schema for payment verification."""
    razorpay_order_id: str = Field(..., description="Razorpay order ID")
    razorpay_payment_id: str = Field(..., description="Razorpay payment ID")
    razorpay_signature: str = Field(..., description="Razorpay signature")


# Webhook Schemas
class WebhookEventResponse(BaseModel):
    """Schema for webhook event response."""
    id: str = Field(..., description="Webhook event ID")
    razorpay_event_id: str = Field(..., description="Razorpay event ID")
    event_type: str = Field(..., description="Event type")
    status: str = Field(..., description="Processing status")
    entity_type: Optional[str] = Field(None, description="Entity type")
    entity_id: Optional[str] = Field(None, description="Entity ID")
    error_message: Optional[str] = Field(None, description="Error message")
    created_at: datetime = Field(..., description="Creation timestamp")
    processed_at: Optional[datetime] = Field(None, description="Processing timestamp")

    class Config:
        from_attributes = True


# Health Check Schemas
class HealthCheckResponse(BaseModel):
    """Schema for health check response."""
    status: str = Field(..., description="Health status")
    service: str = Field(..., description="Service name")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")


class DatabaseHealthResponse(BaseModel):
    """Schema for database health check response."""
    status: str = Field(..., description="Database status")
    connection: bool = Field(..., description="Connection status")
    response_time_ms: Optional[float] = Field(None, description="Response time in milliseconds")


class RazorpayHealthResponse(BaseModel):
    """Schema for Razorpay health check response."""
    status: str = Field(..., description="Razorpay status")
    api_accessible: bool = Field(..., description="API accessibility")
    response_time_ms: Optional[float] = Field(None, description="Response time in milliseconds")


class ComprehensiveHealthResponse(BaseModel):
    """Schema for comprehensive health check response."""
    overall_status: str = Field(..., description="Overall system status")
    services: Dict[str, Dict[str, Any]] = Field(..., description="Individual service statuses")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")


# Error Response Schema
class PaymentErrorResponse(BaseModel):
    """Schema for payment error responses."""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
