from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func, and_
import uuid
from datetime import datetime

from app.models.subscription_plan import SubscriptionPlan
from app.models.user_subscription import UserSubscription
from app.models.organization_subscription import OrganizationSubscription
from app.models.user import User
from app.models.organization import Organization
from app.models.interview import Interview, ScheduledBy
from app.schemas.subscription_schema import (
    SubscriptionPlanCreate,
    SubscriptionPlanUpdate,
    SubscriptionPlanResponse,
    UserSubscriptionResponse,
    OrganizationSubscriptionResponse,
    SubscriptionUsageResponse,
    SubscriptionStatsResponse,
    UserSubscriptionCreateRequest,
    OrganizationSubscriptionCreateRequest,
)
from app.utils.exceptions.subscription_exceptions import (
    SubscriptionLimitExceededException,
    SubscriptionNotFoundException,
    InvalidSubscriptionPlanException,
    SubscriptionAssignmentException,
    SubscriptionPlanNotFoundException,
    SubscriptionPlanCreationException,
    SubscriptionPlanUpdateException,
    SubscriptionUsageUpdateException,
)
from app.utils.exceptions.database_exceptions import DatabaseOperationException


class SubscriptionService:
    def __init__(self, db: Session):
        self.db = db

    def create_subscription_plan(self, plan_data: SubscriptionPlanCreate) -> SubscriptionPlanResponse:
        """Create a new subscription plan."""
        try:
            plan = SubscriptionPlan(
                id=str(uuid.uuid4()),
                name=plan_data.name,
                description=plan_data.description,
                interview_limit=plan_data.interview_limit,
                price=plan_data.price or 0.00,
                is_active=plan_data.is_active,
                features=plan_data.features,
            )
            
            self.db.add(plan)
            self.db.commit()
            self.db.refresh(plan)
            
            return SubscriptionPlanResponse.model_validate(plan)
        except SQLAlchemyError as e:
            self.db.rollback()
            raise SubscriptionPlanCreationException(f"Failed to create subscription plan: {str(e)}")

    def get_subscription_plan(self, plan_id: str) -> SubscriptionPlanResponse:
        """Get a subscription plan by ID."""
        try:
            query = select(SubscriptionPlan).where(SubscriptionPlan.id == plan_id)
            result = self.db.execute(query)
            plan = result.scalar_one_or_none()
            
            if not plan:
                raise SubscriptionPlanNotFoundException(f"Subscription plan with ID {plan_id} not found")
            
            return SubscriptionPlanResponse.model_validate(plan)
        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"Failed to fetch subscription plan: {str(e)}")

    def get_all_subscription_plans(self, include_inactive: bool = False) -> List[SubscriptionPlanResponse]:
        """Get all subscription plans."""
        try:
            query = select(SubscriptionPlan)
            if not include_inactive:
                query = query.where(SubscriptionPlan.is_active == True)
            
            result = self.db.execute(query)
            plans = result.scalars().all()
            
            return [SubscriptionPlanResponse.model_validate(plan) for plan in plans]
        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"Failed to fetch subscription plans: {str(e)}")

    def update_subscription_plan(self, plan_id: str, plan_data: SubscriptionPlanUpdate) -> SubscriptionPlanResponse:
        """Update a subscription plan."""
        try:
            query = select(SubscriptionPlan).where(SubscriptionPlan.id == plan_id)
            result = self.db.execute(query)
            plan = result.scalar_one_or_none()
            
            if not plan:
                raise SubscriptionPlanNotFoundException(f"Subscription plan with ID {plan_id} not found")
            
            # Update fields if provided
            update_data = plan_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(plan, field, value)
            
            plan.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(plan)
            
            return SubscriptionPlanResponse.model_validate(plan)
        except SQLAlchemyError as e:
            self.db.rollback()
            raise SubscriptionPlanUpdateException(f"Failed to update subscription plan: {str(e)}")

    def delete_subscription_plan(self, plan_id: str) -> bool:
        """Delete a subscription plan (soft delete by setting is_active to False)."""
        try:
            query = select(SubscriptionPlan).where(SubscriptionPlan.id == plan_id)
            result = self.db.execute(query)
            plan = result.scalar_one_or_none()
            
            if not plan:
                raise SubscriptionPlanNotFoundException(f"Subscription plan with ID {plan_id} not found")
            
            plan.is_active = False
            plan.updated_at = datetime.utcnow()
            
            self.db.commit()
            return True
        except SQLAlchemyError as e:
            self.db.rollback()
            raise SubscriptionPlanUpdateException(f"Failed to delete subscription plan: {str(e)}")

    def assign_subscription_to_user(self, user_id: str, plan_id: str) -> UserSubscriptionResponse:
        """Assign a subscription plan to a user."""
        try:
            # Check if user already has a subscription
            existing_query = select(UserSubscription).where(UserSubscription.user_id == user_id)
            existing_result = self.db.execute(existing_query)
            existing_subscription = existing_result.scalar_one_or_none()
            
            if existing_subscription:
                # Update existing subscription
                existing_subscription.subscription_plan_id = plan_id
                existing_subscription.interviews_used = 0  # Reset usage
                existing_subscription.activated_at = datetime.utcnow()
                existing_subscription.updated_at = datetime.utcnow()
                subscription = existing_subscription
            else:
                # Create new subscription
                subscription = UserSubscription(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    subscription_plan_id=plan_id,
                    interviews_used=0,
                    activated_at=datetime.utcnow(),
                    is_active=True,
                )
                self.db.add(subscription)
            
            self.db.commit()
            self.db.refresh(subscription)
            
            # Load the subscription with plan details
            return self.get_user_subscription(user_id)
        except SQLAlchemyError as e:
            self.db.rollback()
            raise SubscriptionAssignmentException(f"Failed to assign subscription: {str(e)}")

    def get_user_subscription(self, user_id: str) -> UserSubscriptionResponse:
        """Get user's current subscription."""
        try:
            query = select(UserSubscription).where(
                and_(
                    UserSubscription.user_id == user_id,
                    UserSubscription.is_active == True
                )
            ).options(
                # Eager load the subscription plan
                joinedload(UserSubscription.subscription_plan)
            )
            
            result = self.db.execute(query)
            subscription = result.scalar_one_or_none()
            
            if not subscription:
                raise SubscriptionNotFoundException(f"No active subscription found for user {user_id}")
            
            # Manually load the subscription plan
            plan_query = select(SubscriptionPlan).where(SubscriptionPlan.id == subscription.subscription_plan_id)
            plan_result = self.db.execute(plan_query)
            plan = plan_result.scalar_one()
            
            # Create response with calculated fields
            response_data = {
                "id": subscription.id,
                "user_id": subscription.user_id,
                "subscription_plan_id": subscription.subscription_plan_id,
                "interviews_used": subscription.interviews_used,
                "interviews_remaining": max(0, plan.interview_limit - subscription.interviews_used),
                "activated_at": subscription.activated_at,
                "expires_at": subscription.expires_at,
                "is_active": subscription.is_active,
                "created_at": subscription.created_at,
                "updated_at": subscription.updated_at,
                "subscription_plan": SubscriptionPlanResponse.model_validate(plan)
            }
            
            return UserSubscriptionResponse(**response_data)
        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"Failed to fetch user subscription: {str(e)}")

    def get_subscription_usage(self, user_id: str) -> SubscriptionUsageResponse:
        """Get user's subscription usage summary."""
        try:
            subscription = self.get_user_subscription(user_id)
            
            return SubscriptionUsageResponse(
                interviews_used=subscription.interviews_used,
                interviews_remaining=subscription.interviews_remaining,
                interview_limit=subscription.subscription_plan.interview_limit,
                plan_name=subscription.subscription_plan.name,
                is_limit_reached=subscription.interviews_remaining <= 0,
                can_schedule_interview=subscription.interviews_remaining > 0 and subscription.is_active
            )
        except SubscriptionNotFoundException:
            # If no subscription found, return default values
            return SubscriptionUsageResponse(
                interviews_used=0,
                interviews_remaining=0,
                interview_limit=0,
                plan_name="No Plan",
                is_limit_reached=True,
                can_schedule_interview=False
            )

    def check_interview_limit(self, user_id: str) -> bool:
        """Check if user can schedule another interview."""
        try:
            usage = self.get_subscription_usage(user_id)
            return usage.can_schedule_interview
        except SubscriptionNotFoundException:
            return False

    def increment_interview_usage(self, user_id: str) -> UserSubscriptionResponse:
        """Increment user's interview usage count."""
        try:
            query = select(UserSubscription).where(
                and_(
                    UserSubscription.user_id == user_id,
                    UserSubscription.is_active == True
                )
            )
            result = self.db.execute(query)
            subscription = result.scalar_one_or_none()
            
            if not subscription:
                raise SubscriptionNotFoundException(f"No active subscription found for user {user_id}")
            
            subscription.interviews_used += 1
            subscription.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(subscription)
            
            return self.get_user_subscription(user_id)
        except SQLAlchemyError as e:
            self.db.rollback()
            raise SubscriptionUsageUpdateException(f"Failed to update interview usage: {str(e)}")

    def get_default_subscription_plan(self) -> Optional[SubscriptionPlanResponse]:
        """Get the default subscription plan (Basic Plan)."""
        try:
            query = select(SubscriptionPlan).where(
                and_(
                    SubscriptionPlan.name == "Basic Plan",
                    SubscriptionPlan.is_active == True
                )
            )
            result = self.db.execute(query)
            plan = result.scalar_one_or_none()
            
            if plan:
                return SubscriptionPlanResponse.model_validate(plan)
            return None
        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"Failed to fetch default subscription plan: {str(e)}")

    def create_default_subscription_plan(self, interview_limit: int = 5) -> SubscriptionPlanResponse:
        """Create the default Basic Plan if it doesn't exist."""
        try:
            # Check if Basic Plan already exists
            existing_plan = self.get_default_subscription_plan()
            if existing_plan:
                return existing_plan
            
            # Create Basic Plan
            plan_data = SubscriptionPlanCreate(
                name="Basic Plan",
                description="Default subscription plan for individual users",
                interview_limit=interview_limit,
                price=0.00,
                is_active=True,
                features={"type": "basic", "support": "community"}
            )
            
            return self.create_subscription_plan(plan_data)
        except Exception as e:
            raise SubscriptionPlanCreationException(f"Failed to create default subscription plan: {str(e)}")

    def get_subscription_stats(self) -> SubscriptionStatsResponse:
        """Get subscription statistics for admin dashboard."""
        try:
            # Total users with subscriptions
            total_users_query = select(func.count(UserSubscription.id)).where(UserSubscription.is_active == True)
            total_users_result = self.db.execute(total_users_query)
            total_users = total_users_result.scalar()
            
            # Total organizations with subscriptions
            total_orgs_query = select(func.count(OrganizationSubscription.id)).where(OrganizationSubscription.is_active == True)
            total_orgs_result = self.db.execute(total_orgs_query)
            total_organizations = total_orgs_result.scalar()
            
            # Total active subscriptions (users + organizations)
            total_active_subscriptions = (total_users or 0) + (total_organizations or 0)
            
            # Total plans
            total_plans_query = select(func.count(SubscriptionPlan.id)).where(SubscriptionPlan.is_active == True)
            total_plans_result = self.db.execute(total_plans_query)
            total_plans = total_plans_result.scalar()
            
            # Usage by plan (combining user and organization subscriptions)
            user_usage_query = select(
                SubscriptionPlan.name,
                SubscriptionPlan.interview_limit,
                func.count(UserSubscription.id).label('subscriber_count'),
                func.avg(UserSubscription.interviews_used).label('avg_usage'),
                func.sum(UserSubscription.interviews_used).label('total_usage')
            ).join(
                UserSubscription, SubscriptionPlan.id == UserSubscription.subscription_plan_id
            ).where(
                and_(
                    SubscriptionPlan.is_active == True,
                    UserSubscription.is_active == True
                )
            ).group_by(
                SubscriptionPlan.id, SubscriptionPlan.name, SubscriptionPlan.interview_limit
            )
            
            org_usage_query = select(
                SubscriptionPlan.name,
                SubscriptionPlan.interview_limit,
                func.count(OrganizationSubscription.id).label('subscriber_count'),
                func.avg(OrganizationSubscription.interviews_used).label('avg_usage'),
                func.sum(OrganizationSubscription.interviews_used).label('total_usage')
            ).join(
                OrganizationSubscription, SubscriptionPlan.id == OrganizationSubscription.subscription_plan_id
            ).where(
                and_(
                    SubscriptionPlan.is_active == True,
                    OrganizationSubscription.is_active == True
                )
            ).group_by(
                SubscriptionPlan.id, SubscriptionPlan.name, SubscriptionPlan.interview_limit
            )
            
            user_usage_result = self.db.execute(user_usage_query)
            org_usage_result = self.db.execute(org_usage_query)
            
            usage_data = {}
            
            # Process user subscription data
            for row in user_usage_result:
                usage_data[row.name] = {
                    "interview_limit": row.interview_limit,
                    "user_count": row.subscriber_count,
                    "organization_count": 0,
                    "total_subscribers": row.subscriber_count,
                    "average_usage": float(row.avg_usage or 0),
                    "total_usage": row.total_usage or 0
                }
            
            # Process organization subscription data and merge with user data
            for row in org_usage_result:
                if row.name in usage_data:
                    # Plan exists in user data, merge organization data
                    usage_data[row.name]["organization_count"] = row.subscriber_count
                    usage_data[row.name]["total_subscribers"] += row.subscriber_count
                    # Calculate combined average usage
                    total_users_for_plan = usage_data[row.name]["user_count"]
                    total_orgs_for_plan = row.subscriber_count
                    total_subscribers = total_users_for_plan + total_orgs_for_plan
                    if total_subscribers > 0:
                        combined_total_usage = usage_data[row.name]["total_usage"] + (row.total_usage or 0)
                        usage_data[row.name]["average_usage"] = combined_total_usage / total_subscribers
                        usage_data[row.name]["total_usage"] = combined_total_usage
                else:
                    # Plan only has organization subscribers
                    usage_data[row.name] = {
                        "interview_limit": row.interview_limit,
                        "user_count": 0,
                        "organization_count": row.subscriber_count,
                        "total_subscribers": row.subscriber_count,
                        "average_usage": float(row.avg_usage or 0),
                        "total_usage": row.total_usage or 0
                    }
            
            return SubscriptionStatsResponse(
                total_users=total_users or 0,
                total_organizations=total_organizations or 0,
                total_active_subscriptions=total_active_subscriptions or 0,
                total_plans=total_plans or 0,
                usage_by_plan=usage_data
            )
        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"Failed to fetch subscription stats: {str(e)}")

    # Organization Subscription Methods
    def assign_subscription_to_organization(self, organization_id: str, plan_id: str) -> OrganizationSubscriptionResponse:
        """Assign a subscription plan to an organization."""
        try:
            # Check if organization already has a subscription
            existing_query = select(OrganizationSubscription).where(OrganizationSubscription.organization_id == organization_id)
            existing_result = self.db.execute(existing_query)
            existing_subscription = existing_result.scalar_one_or_none()
            
            if existing_subscription:
                # Update existing subscription
                existing_subscription.subscription_plan_id = plan_id
                existing_subscription.interviews_used = 0  # Reset usage
                existing_subscription.activated_at = datetime.utcnow()
                existing_subscription.updated_at = datetime.utcnow()
                subscription = existing_subscription
            else:
                # Create new subscription
                subscription = OrganizationSubscription(
                    id=str(uuid.uuid4()),
                    organization_id=organization_id,
                    subscription_plan_id=plan_id,
                    interviews_used=0,
                    activated_at=datetime.utcnow(),
                    is_active=True,
                )
                self.db.add(subscription)
            
            self.db.commit()
            self.db.refresh(subscription)
            
            # Load the subscription with plan details
            return self.get_organization_subscription(organization_id)
        except SQLAlchemyError as e:
            self.db.rollback()
            raise SubscriptionAssignmentException(f"Failed to assign subscription to organization: {str(e)}")

    def get_organization_subscription(self, organization_id: str) -> OrganizationSubscriptionResponse:
        """Get organization's current subscription."""
        try:
            query = select(OrganizationSubscription).where(
                and_(
                    OrganizationSubscription.organization_id == organization_id,
                    OrganizationSubscription.is_active == True
                )
            ).options(
                # Eager load the subscription plan
                joinedload(OrganizationSubscription.subscription_plan)
            )
            
            result = self.db.execute(query)
            subscription = result.scalar_one_or_none()
            
            if not subscription:
                raise SubscriptionNotFoundException(f"No active subscription found for organization {organization_id}")
            
            # Manually load the subscription plan
            plan_query = select(SubscriptionPlan).where(SubscriptionPlan.id == subscription.subscription_plan_id)
            plan_result = self.db.execute(plan_query)
            plan = plan_result.scalar_one()
            
            # Create response with calculated fields
            response_data = {
                "id": subscription.id,
                "organization_id": subscription.organization_id,
                "subscription_plan_id": subscription.subscription_plan_id,
                "interviews_used": subscription.interviews_used,
                "interviews_remaining": max(0, plan.interview_limit - subscription.interviews_used),
                "activated_at": subscription.activated_at,
                "expires_at": subscription.expires_at,
                "is_active": subscription.is_active,
                "created_at": subscription.created_at,
                "updated_at": subscription.updated_at,
                "subscription_plan": SubscriptionPlanResponse.model_validate(plan)
            }
            
            return OrganizationSubscriptionResponse(**response_data)
        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"Failed to fetch organization subscription: {str(e)}")

    def get_organization_subscription_usage(self, organization_id: str) -> SubscriptionUsageResponse:
        """Get organization's subscription usage summary."""
        try:
            subscription = self.get_organization_subscription(organization_id)
            
            return SubscriptionUsageResponse(
                interviews_used=subscription.interviews_used,
                interviews_remaining=subscription.interviews_remaining,
                interview_limit=subscription.subscription_plan.interview_limit,
                plan_name=subscription.subscription_plan.name,
                is_limit_reached=subscription.interviews_remaining <= 0,
                can_schedule_interview=subscription.interviews_remaining > 0 and subscription.is_active
            )
        except SubscriptionNotFoundException:
            # If no subscription found, return default values
            return SubscriptionUsageResponse(
                interviews_used=0,
                interviews_remaining=0,
                interview_limit=0,
                plan_name="No Plan",
                is_limit_reached=True,
                can_schedule_interview=False
            )

    def check_organization_interview_limit(self, organization_id: str) -> bool:
        """Check if organization can schedule another interview."""
        try:
            usage = self.get_organization_subscription_usage(organization_id)
            return usage.can_schedule_interview
        except SubscriptionNotFoundException:
            return False

    def increment_organization_interview_usage(self, organization_id: str) -> OrganizationSubscriptionResponse:
        """Increment organization's interview usage count."""
        try:
            query = select(OrganizationSubscription).where(
                and_(
                    OrganizationSubscription.organization_id == organization_id,
                    OrganizationSubscription.is_active == True
                )
            )
            result = self.db.execute(query)
            subscription = result.scalar_one_or_none()
            
            if not subscription:
                raise SubscriptionNotFoundException(f"No active subscription found for organization {organization_id}")
            
            subscription.interviews_used += 1
            subscription.updated_at = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(subscription)
            
            return self.get_organization_subscription(organization_id)
        except SQLAlchemyError as e:
            self.db.rollback()
            raise SubscriptionUsageUpdateException(f"Failed to update organization interview usage: {str(e)}")

    def get_default_organization_subscription_plan(self) -> Optional[SubscriptionPlanResponse]:
        """Get the default subscription plan for organizations (Organization Basic Plan)."""
        try:
            query = select(SubscriptionPlan).where(
                and_(
                    SubscriptionPlan.name == "Organization Basic Plan",
                    SubscriptionPlan.is_active == True
                )
            )
            result = self.db.execute(query)
            plan = result.scalar_one_or_none()
            
            if plan:
                return SubscriptionPlanResponse.model_validate(plan)
            return None
        except SQLAlchemyError as e:
            raise DatabaseOperationException(f"Failed to fetch default organization subscription plan: {str(e)}")

    def create_default_organization_subscription_plan(self, interview_limit: int = 10) -> SubscriptionPlanResponse:
        """Create the default Organization Basic Plan if it doesn't exist."""
        try:
            # Check if Organization Basic Plan already exists
            existing_plan = self.get_default_organization_subscription_plan()
            if existing_plan:
                return existing_plan
            
            # Create Organization Basic Plan
            plan_data = SubscriptionPlanCreate(
                name="Organization Basic Plan",
                description="Default subscription plan for organizations",
                interview_limit=interview_limit,
                price=0.00,
                is_active=True,
                features={"type": "organization_basic", "support": "community"}
            )
            
            return self.create_subscription_plan(plan_data)
        except Exception as e:
            raise SubscriptionPlanCreationException(f"Failed to create default organization subscription plan: {str(e)}")