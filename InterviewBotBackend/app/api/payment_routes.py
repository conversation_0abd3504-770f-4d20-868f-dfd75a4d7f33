"""
Payment API Routes

This module defines the API endpoints for payment operations including:
- Plan management
- Order creation and management
- Payment verification
- Webhook handling
- Health checks
"""

import structlog
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request, Header
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.core.auth_guard import BaseAuthGuard, roles_required
from app.schemas.user_schema import RegistrationType
from app.services.razorpay_service import RazorpayService
from app.models.plan import Plan
from app.schemas.payment_schema import (
    PlanCreate,
    PlanUpdate,
    PlanResponse,
    OrderCreate,
    OrderResponse,
    TransactionResponse,
    PaymentVerification,
    WebhookEventResponse,
    HealthCheckResponse,
    DatabaseHealthResponse,
    RazorpayHealthResponse,
    ComprehensiveHealthResponse,
    PaymentErrorResponse,
)

logger = structlog.get_logger()

# Create routers for different endpoint groups
plans_router = APIRouter(prefix="/plans", tags=["Payment Plans"])
orders_router = APIRouter(prefix="/orders", tags=["Payment Orders"])
payments_router = APIRouter(prefix="/payments", tags=["Payments"])
webhooks_router = APIRouter(prefix="/webhooks", tags=["Payment Webhooks"])
health_router = APIRouter(prefix="/health", tags=["Payment Health"])


# Plan Management Endpoints
@plans_router.post("/", response_model=PlanResponse, status_code=status.HTTP_201_CREATED)
async def create_plan(
    plan_data: PlanCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(roles_required([RegistrationType.admin])),
):
    """
    Create a new subscription plan.
    
    Only administrators can create plans.
    """
    try:
        razorpay_service = RazorpayService(db)
        plan = razorpay_service.create_plan(plan_data, current_user["user_id"])
        
        logger.info(
            "Plan created via API",
            plan_id=plan.id,
            plan_name=plan.name,
            admin_id=current_user["user_id"]
        )
        
        return plan
        
    except Exception as e:
        logger.error("Failed to create plan via API", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create plan: {str(e)}"
        )


@plans_router.get("/", response_model=List[PlanResponse])
async def get_plans(
    include_inactive: bool = False,
    db: Session = Depends(get_db),
    current_user: dict = Depends(BaseAuthGuard()),
):
    """
    Get all subscription plans.
    
    Regular users see only active plans.
    Administrators can include inactive plans.
    """
    try:
        razorpay_service = RazorpayService(db)
        
        # Check if user is admin
        is_admin = current_user.get("registration_type") == RegistrationType.admin
        
        if include_inactive and not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can view inactive plans"
            )
        
        if include_inactive:
            plans = razorpay_service.db.query(Plan).all()
        else:
            plans = razorpay_service.get_active_plans()
        
        return plans
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get plans via API", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve plans"
        )


@plans_router.get("/{plan_id}", response_model=PlanResponse)
async def get_plan(
    plan_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(BaseAuthGuard()),
):
    """Get a specific plan by ID."""
    try:
        razorpay_service = RazorpayService(db)
        plan = razorpay_service.get_plan(plan_id)
        
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Plan not found: {plan_id}"
            )
        
        # Non-admin users can only see active plans
        if not plan.is_active and current_user.get("registration_type") != RegistrationType.admin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Plan not found: {plan_id}"
            )
        
        return plan
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get plan via API", plan_id=plan_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve plan"
        )


@plans_router.put("/{plan_id}", response_model=PlanResponse)
async def update_plan(
    plan_id: str,
    plan_data: PlanUpdate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(roles_required([RegistrationType.admin])),
):
    """
    Update an existing plan.
    
    Only administrators can update plans.
    """
    try:
        razorpay_service = RazorpayService(db)
        plan = razorpay_service.update_plan(plan_id, plan_data, current_user["user_id"])
        
        logger.info(
            "Plan updated via API",
            plan_id=plan_id,
            admin_id=current_user["user_id"]
        )
        
        return plan
        
    except Exception as e:
        logger.error("Failed to update plan via API", plan_id=plan_id, error=str(e))
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Plan not found: {plan_id}"
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update plan: {str(e)}"
        )


# Order Management Endpoints
@orders_router.post("/", response_model=OrderResponse, status_code=status.HTTP_201_CREATED)
async def create_order(
    order_data: OrderCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(BaseAuthGuard()),
):
    """
    Create a new payment order.
    
    Creates a Razorpay order for the specified plan.
    """
    try:
        razorpay_service = RazorpayService(db)
        order = razorpay_service.create_order(order_data, current_user["user_id"])
        
        logger.info(
            "Order created via API",
            order_id=order.id,
            razorpay_order_id=order.razorpay_order_id,
            plan_id=order_data.plan_id,
            user_id=current_user["user_id"]
        )
        
        return order
        
    except Exception as e:
        logger.error("Failed to create order via API", error=str(e))
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        elif "not active" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create order: {str(e)}"
        )


@orders_router.get("/{order_id}", response_model=OrderResponse)
async def get_order(
    order_id: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(BaseAuthGuard()),
):
    """
    Get order details.
    
    Users can only access their own orders.
    Administrators can access any order.
    """
    try:
        razorpay_service = RazorpayService(db)
        order = razorpay_service.get_order(order_id)
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Order not found: {order_id}"
            )
        
        # Check access permissions
        is_admin = current_user.get("registration_type") == RegistrationType.admin
        if not is_admin and order.user_id != current_user["user_id"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only view your own orders"
            )
        
        return order
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get order via API", order_id=order_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve order"
        )


# Payment Verification Endpoint
@payments_router.post("/verify", response_model=TransactionResponse)
async def verify_payment(
    verification_data: PaymentVerification,
    db: Session = Depends(get_db),
    current_user: dict = Depends(BaseAuthGuard()),
):
    """
    Verify payment and create transaction record.
    
    Verifies the payment signature and creates a transaction record.
    """
    try:
        razorpay_service = RazorpayService(db)
        transaction = razorpay_service.verify_payment(verification_data, current_user["user_id"])
        
        logger.info(
            "Payment verified via API",
            transaction_id=transaction.id,
            payment_id=verification_data.razorpay_payment_id,
            order_id=verification_data.razorpay_order_id,
            user_id=current_user["user_id"]
        )
        
        return transaction
        
    except Exception as e:
        logger.error("Failed to verify payment via API", error=str(e))
        if "invalid" in str(e).lower() or "signature" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        elif "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Payment verification failed: {str(e)}"
        )


# Webhook Endpoints
@webhooks_router.post("/razorpay", response_model=WebhookEventResponse)
async def handle_razorpay_webhook(
    request: Request,
    db: Session = Depends(get_db),
    x_razorpay_signature: str = Header(..., alias="X-Razorpay-Signature"),
):
    """
    Handle Razorpay webhook events.

    Processes webhook events from Razorpay for payment status updates.
    """
    try:
        # Get request body
        body = await request.body()
        payload = await request.json()

        razorpay_service = RazorpayService(db)
        webhook_event = razorpay_service.process_webhook(payload, x_razorpay_signature)

        logger.info(
            "Webhook processed via API",
            event_id=webhook_event.id,
            razorpay_event_id=webhook_event.razorpay_event_id,
            event_type=webhook_event.event_type,
            status=webhook_event.status
        )

        return webhook_event

    except Exception as e:
        logger.error("Failed to process webhook via API", error=str(e))
        if "signature" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid webhook signature"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Webhook processing failed: {str(e)}"
        )


# Health Check Endpoints
@health_router.get("/", response_model=HealthCheckResponse)
async def basic_health_check():
    """Basic health check for payment service."""
    return HealthCheckResponse(
        status="healthy",
        service="payment-service"
    )


@health_router.get("/db", response_model=DatabaseHealthResponse)
async def database_health_check(db: Session = Depends(get_db)):
    """Check database connectivity for payment service."""
    try:
        from datetime import datetime
        start_time = datetime.utcnow()

        # Simple database query to test connectivity
        db.execute("SELECT 1")

        end_time = datetime.utcnow()
        response_time = (end_time - start_time).total_seconds() * 1000

        return DatabaseHealthResponse(
            status="healthy",
            connection=True,
            response_time_ms=response_time
        )

    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        return DatabaseHealthResponse(
            status="unhealthy",
            connection=False
        )


@health_router.get("/razorpay", response_model=RazorpayHealthResponse)
async def razorpay_health_check(db: Session = Depends(get_db)):
    """Check Razorpay API connectivity."""
    try:
        razorpay_service = RazorpayService(db)
        health_result = razorpay_service.check_razorpay_health()

        return RazorpayHealthResponse(
            status=health_result["status"],
            api_accessible=health_result["api_accessible"],
            response_time_ms=health_result.get("response_time_ms")
        )

    except Exception as e:
        logger.error("Razorpay health check failed", error=str(e))
        return RazorpayHealthResponse(
            status="unhealthy",
            api_accessible=False
        )


@health_router.get("/all", response_model=ComprehensiveHealthResponse)
async def comprehensive_health_check(db: Session = Depends(get_db)):
    """Comprehensive health check for all payment service components."""
    try:
        services = {}
        overall_healthy = True

        # Check basic service health
        services["payment_service"] = {
            "status": "healthy",
            "details": "Payment service is running"
        }

        # Check database health
        try:
            from datetime import datetime
            start_time = datetime.utcnow()
            db.execute("SELECT 1")
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000

            services["database"] = {
                "status": "healthy",
                "connection": True,
                "response_time_ms": response_time
            }
        except Exception as e:
            services["database"] = {
                "status": "unhealthy",
                "connection": False,
                "error": str(e)
            }
            overall_healthy = False

        # Check Razorpay health
        try:
            razorpay_service = RazorpayService(db)
            health_result = razorpay_service.check_razorpay_health()
            services["razorpay"] = health_result

            if health_result["status"] != "healthy":
                overall_healthy = False

        except Exception as e:
            services["razorpay"] = {
                "status": "unhealthy",
                "api_accessible": False,
                "error": str(e)
            }
            overall_healthy = False

        return ComprehensiveHealthResponse(
            overall_status="healthy" if overall_healthy else "unhealthy",
            services=services
        )

    except Exception as e:
        logger.error("Comprehensive health check failed", error=str(e))
        return ComprehensiveHealthResponse(
            overall_status="unhealthy",
            services={
                "error": {
                    "status": "unhealthy",
                    "error": str(e)
                }
            }
        )


# Create main payment router that includes all sub-routers
payment_router = APIRouter(prefix="/payments", tags=["Payments"])

# Include all sub-routers
payment_router.include_router(plans_router)
payment_router.include_router(orders_router)
payment_router.include_router(payments_router)
payment_router.include_router(webhooks_router)
payment_router.include_router(health_router)
