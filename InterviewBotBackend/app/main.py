"""
Interview Backend Main Application Module

This module serves as the entry point for the FastAPI application, handling:
- Application lifecycle management (startup/shutdown)
- Middleware configuration (CORS, authentication)
- Router registration and API endpoint organization
- Global exception handling for authentication errors
- Database initialization and health monitoring

The application follows a modular architecture with separate routers for different
functional domains (users, jobs, interviews, etc.).
"""

import structlog
import uvicorn
from contextlib import asynccontextmanager
from typing import Any, Dict

from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import settings
from app.db.init_db import init_db
from app.api import (
    user_routes,
    s3_routes,
    mcp_client_routes,
    interview_routes,
    admin_routes,
    livekit_routes,
    subscription_routes,
    job_routes,
    resume_routes,
)
from app.utils.exceptions.auth_exceptions import (
    MissingBearerTokenException,
    InvalidBearerSchemeException,
    ExpiredTokenException,
    InvalidTokenException,
    UnauthorizedException,
    InsufficientRoleException,
)

# Initialize structured logger for consistent logging across the application
logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager handling startup and shutdown events.
    
    Startup:
    - Initializes database tables
    - Logs successful startup
    
    Shutdown:
    - Performs graceful cleanup
    - Logs shutdown completion
    
    Args:
        app: FastAPI application instance
        
    Yields:
        None: Control to the application during its lifetime
        
    Raises:
        SQLAlchemyError: If database initialization fails
    """
    try:
        # Database initialization at startup
        logger.info("Initializing database tables...")
        init_db()
        logger.info("Database tables initialized successfully")
    except SQLAlchemyError as e:
        logger.error("Failed to initialize database", error=str(e), exc_info=True)
        # Re-raise to prevent application startup with broken database
        raise
    except Exception as e:
        logger.error("Unexpected error during startup", error=str(e), exc_info=True)
        raise

    # Startup complete - application is ready to serve requests
    logger.info("API server startup completed successfully")
    
    try:
        yield
    finally:
        # Graceful shutdown
        logger.info("Initiating API server shutdown")


# Create FastAPI application instance with comprehensive metadata
app = FastAPI(
    title="Interview Backend API",
    description="""
    A comprehensive interview management system providing:
    - User and organization management
    - Job posting and application workflows
    - Interview scheduling and management
    - File storage and processing capabilities
    - Real-time communication features
    """,
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
)


async def auth_exception_handler(request: Request, exc: Any) -> JSONResponse:
    """
    Global exception handler for authentication-related errors.
    
    Provides consistent error responses for all authentication failures
    including missing tokens, invalid schemes, expired tokens, etc.
    
    Args:
        request: The incoming HTTP request
        exc: The authentication exception instance
        
    Returns:
        JSONResponse: Standardized error response with appropriate status code
    """
    logger.warning(
        "Authentication error occurred",
        error_type=type(exc).__name__,
        error_message=exc.message,
        path=request.url.path,
        method=request.method,
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.message,
            "error_type": type(exc).__name__,
        }
    )


# Register authentication exception handlers
for exception_class in [
    MissingBearerTokenException,
    InvalidBearerSchemeException,
    ExpiredTokenException,
    InvalidTokenException,
    UnauthorizedException,
    InsufficientRoleException,
]:
    app.exception_handler(exception_class)(auth_exception_handler)


# Configure CORS middleware for cross-origin requests
# Note: In production, replace "*" with specific allowed origins for security
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # TODO: Replace with specific origins in production
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
)


def register_routers() -> None:
    """
    Register all API routers with the FastAPI application.
    
    Organizes routes by functional domain:
    - Authentication and user management
    - Job posting and application workflows
    - Interview scheduling and management
    - File storage and administrative functions
    """
    # User and authentication routes
    app.include_router(user_routes.auth_router, prefix=settings.API_V1_STR)
    app.include_router(user_routes.user_router, prefix=settings.API_V1_STR)
    app.include_router(user_routes.organization_router, prefix=settings.API_V1_STR)
    
    # Job management routes
    app.include_router(job_routes.job_router, prefix=settings.API_V1_STR)
    app.include_router(job_routes.job_application_router, prefix=settings.API_V1_STR)
    app.include_router(job_routes.job_browse_router, prefix=settings.API_V1_STR)
    
    # Resume management routes
    app.include_router(resume_routes.router, prefix=settings.API_V1_STR)
    
    # Interview and communication routes
    app.include_router(interview_routes.interview_router, prefix=settings.API_V1_STR)
    app.include_router(livekit_routes.livekit_router, prefix=settings.API_V1_STR)
    
    # Infrastructure and utility routes
    app.include_router(s3_routes.s3_router, prefix=settings.API_V1_STR)
    app.include_router(mcp_client_routes.mcp_client_router, prefix=settings.API_V1_STR)
    app.include_router(admin_routes.admin_router, prefix=settings.API_V1_STR)
    app.include_router(subscription_routes.router, prefix=settings.API_V1_STR)


# Register all application routes
register_routers()


@app.get("/health", tags=["Health"])
async def health_check() -> Dict[str, str]:
    """
    Health check endpoint for monitoring application status.
    
    Returns:
        Dict[str, str]: Simple status indicator
    """
    return {"status": "healthy", "service": "interview-backend"}


@app.get("/", tags=["Root"])
async def root() -> Dict[str, str]:
    """
    Root endpoint providing basic API information.
    
    Returns:
        Dict[str, str]: API welcome message and version
    """
    return {
        "message": "Interview Backend API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
    }


def start() -> None:
    """
    Application entry point for starting the uvicorn server.
    
    Configures and starts the ASGI server with appropriate settings
    based on the current environment (development/production).
    """
    try:
        port = int(settings.PORT)
        
        logger.info(
            "Starting uvicorn server",
            host="0.0.0.0",
            port=port,
            reload=settings.DEBUG,
            environment=settings.ENV,
        )
        
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=port,
            reload=settings.DEBUG,
            log_level="info" if not settings.DEBUG else "debug",
            access_log=True,
        )
        
    except ValueError as e:
        logger.error("Invalid port configuration", error=str(e))
        raise
    except Exception as e:
        logger.error("Failed to start server", error=str(e), exc_info=True)
        raise


if __name__ == "__main__":
    start()
