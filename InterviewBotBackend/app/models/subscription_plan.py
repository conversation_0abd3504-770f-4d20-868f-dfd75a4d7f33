from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    <PERSON><PERSON>an,
    Integer,
    JSON,
    Numeric,
)
from sqlalchemy.orm import relationship
from app.models.base import Base


class SubscriptionPlan(Base):
    __tablename__ = "subscription_plans"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    interview_limit = Column(Integer, nullable=False)
    price = Column(Numeric(10, 2), default=0.00)
    is_active = Column(Boolean, default=True)
    features = Column(JSON, nullable=True)  # For future extensibility
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user_subscriptions = relationship(
        "UserSubscription",
        back_populates="subscription_plan",
        cascade="all, delete-orphan"
    )
    
    organization_subscriptions = relationship(
        "OrganizationSubscription",
        back_populates="subscription_plan",
        cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<SubscriptionPlan {self.name} - {self.interview_limit} interviews>"