from datetime import datetime
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Boolean,
    JSON,
)
from app.models.base import Base
from sqlalchemy.orm import relationship

class Organization(Base):
    __tablename__ = "organizations"

    id = Column(String, primary_key=True)
    email = Column(String, unique=True, nullable=False, index=True)
    organization_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=True)  # Nullable for OAuth users
    is_active = Column(Boolean, default=True)
    is_oauth_org = Column(Boolean, default=False)
    oauth_provider = Column(String, nullable=True)
    google_id = Column(String, unique=True, nullable=True)  # New column for Google ID

    # Email verification fields
    is_email_verified = Column(Boolean, default=False)
    email_verified_at = Column(DateTime, nullable=True)

    skill_sets = Column(JSON, nullable=True)  # e.g., ["Python", "UI Design"]
    job_roles = Column(JSON, nullable=True)  # e.g., ["Backend Developer", "Recruiter"]

    # Store job descriptions not tied to skill sets or job roles
    # Format: {"jd_id1": {"name": "JD Name", "description": "JD text...", "link": "S3 URL"}, ...}
    standalone_job_descriptions = Column(JSON, nullable=True)
    is_setup_complete = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Profile fields
    position = Column(String, nullable=True)  # e.g., "Product Design Lead"
    location = Column(String, nullable=True)  # e.g., "London, UK"
    phone_number = Column(String, nullable=True)  # e.g., "+97-**********"
    bio = Column(String, nullable=True)       # About text
    website = Column(String, nullable=True)   # Personal website

    # Social media accounts
    instagram = Column(String, nullable=True)
    tiktok = Column(String, nullable=True)
    youtube = Column(String, nullable=True)
    twitter = Column(String, nullable=True)
    linkedin = Column(String, nullable=True)

    # Profile picture URL or path
    profile_picture = Column(String, nullable=True)

    interviews = relationship(
        "Interview",
        back_populates="organization",
        foreign_keys="[Interview.organization_id]",
    )

    job_postings = relationship(
        "JobPosting",
        back_populates="organization",
        cascade="all, delete-orphan"
    )

    # Subscription relationship
    subscription = relationship(
        "OrganizationSubscription",
        back_populates="organization",
        uselist=False,
        cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Organization {self.email}>"
